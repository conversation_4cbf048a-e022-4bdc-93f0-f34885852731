#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第一阶段完整测试脚本：合理性检查
测试所有问答对100-201文件夹内的文件
"""

import json
import os
import time
from datetime import datetime
from multi_stage_compliance_evaluator import MultiStageComplianceEvaluator
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('stage1_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def test_stage1_all_files():
    """测试第一阶段：对所有文件进行合理性检查"""
    print("=" * 80)
    print("第一阶段完整测试：合理性检查")
    print("=" * 80)
    
    # 创建评估器
    evaluator = MultiStageComplianceEvaluator()
    
    # 输入和输出目录
    input_folder = "问答对100-201"
    output_folder = "第一阶段测试结果"
    os.makedirs(output_folder, exist_ok=True)
    
    # 获取所有JSON文件
    if not os.path.exists(input_folder):
        print(f"❌ 输入文件夹不存在: {input_folder}")
        return False
    
    json_files = [f for f in os.listdir(input_folder) if f.endswith('.json')]
    json_files.sort()
    
    print(f"📁 找到 {len(json_files)} 个JSON文件")
    print(f"📂 输出目录: {output_folder}")
    print()
    
    # 统计信息
    total_files = len(json_files)
    success_count = 0
    reasonable_count = 0
    unreasonable_count = 0
    error_count = 0
    
    all_results = []
    
    # 处理每个文件
    for i, filename in enumerate(json_files, 1):
        file_path = os.path.join(input_folder, filename)
        file_id = os.path.splitext(filename)[0]
        
        print(f"🔄 处理 {i}/{total_files}: {filename}")
        
        try:
            # 加载问答对数据
            qa_data = evaluator.load_qa_file(file_path)
            if qa_data is None:
                print(f"   ❌ 文件加载失败")
                error_count += 1
                continue
            
            # 显示问题内容
            question = qa_data.get("问题", "")
            answer = qa_data.get("回答", "")
            category = qa_data.get("类别", "")
            print(f"   📝 类别: {category}")
            print(f"   📝 问题: {question[:50]}{'...' if len(question) > 50 else ''}")
            print(f"   📝 回答: {answer[:50]}{'...' if len(answer) > 50 else ''}")
            
            # 执行第一阶段测试
            stage1_result = evaluator.stage1_reasonableness_check(qa_data)
            
            if stage1_result.get("error"):
                print(f"   ❌ API调用失败: {stage1_result['error']}")
                error_count += 1
                continue
            
            # 提取关键结果
            judgment = stage1_result.get("reasonableness_judgment", "未知")
            reasoning = stage1_result.get("reasoning", "无理由")
            result_status = stage1_result.get("stage1_result", "未知")
            
            print(f"   ✅ 合理性判断: {judgment}")
            print(f"   🔄 阶段结果: {result_status}")
            print(f"   💭 理由: {reasoning[:80]}{'...' if len(reasoning) > 80 else ''}")
            
            # 统计
            success_count += 1
            if judgment == "合理":
                reasonable_count += 1
            elif judgment == "不合理":
                unreasonable_count += 1
            
            # 构建完整结果
            complete_result = {
                "文件名": filename,
                "文件ID": file_id,
                "原始数据": qa_data,
                "第一阶段结果": {
                    "reasonableness_judgment": judgment,
                    "reasoning": reasoning,
                    "stage1_result": result_status,
                    "stage": "stage1_reasonableness"
                },
                "测试时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "测试状态": "成功"
            }
            
            # 保存单个文件结果
            output_filename = f"{file_id}_stage1_result.json"
            output_filepath = os.path.join(output_folder, output_filename)
            
            with open(output_filepath, 'w', encoding='utf-8') as f:
                json.dump(complete_result, f, ensure_ascii=False, indent=2)
            
            all_results.append(complete_result)
            
            # 添加延迟避免API限制
            time.sleep(1)
            
        except Exception as e:
            print(f"   ❌ 处理失败: {e}")
            error_count += 1
            logging.error(f"处理文件 {filename} 时出错: {e}")
            continue
        
        print()
    
    # 生成汇总报告
    generate_stage1_summary(all_results, output_folder, {
        'total_files': total_files,
        'success_count': success_count,
        'reasonable_count': reasonable_count,
        'unreasonable_count': unreasonable_count,
        'error_count': error_count
    })
    
    return True

def generate_stage1_summary(results, output_folder, stats):
    """生成第一阶段汇总报告"""
    
    # 保存汇总JSON
    summary_file = os.path.join(output_folder, f"stage1_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    summary_data = {
        "测试时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "统计信息": stats,
        "详细结果": results
    }
    
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(summary_data, f, ensure_ascii=False, indent=2)
    
    # 生成文本报告
    report_file = os.path.join(output_folder, f"stage1_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt")
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("=" * 80 + "\n")
        f.write("第一阶段测试报告：合理性检查\n")
        f.write("=" * 80 + "\n\n")
        
        f.write(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"总文件数: {stats['total_files']}\n")
        f.write(f"成功处理: {stats['success_count']}\n")
        f.write(f"处理失败: {stats['error_count']}\n")
        f.write(f"成功率: {stats['success_count']/stats['total_files']*100:.1f}%\n\n")
        
        f.write("合理性判断分布:\n")
        f.write("-" * 40 + "\n")
        if stats['success_count'] > 0:
            f.write(f"合理: {stats['reasonable_count']} 个 ({stats['reasonable_count']/stats['success_count']*100:.1f}%)\n")
            f.write(f"不合理: {stats['unreasonable_count']} 个 ({stats['unreasonable_count']/stats['success_count']*100:.1f}%)\n\n")
        
        # 详细结果列表
        f.write("详细结果:\n")
        f.write("-" * 40 + "\n")
        for result in results:
            stage1_data = result.get("第一阶段结果", {})
            judgment = stage1_data.get("reasonableness_judgment", "未知")
        
        f.write(f"\n详细结果已保存到: {summary_file}\n")
    
    print("=" * 80)
    print("第一阶段测试完成")
    print("=" * 80)
    print(f"📊 总文件数: {stats['total_files']}")
    print(f"✅ 成功处理: {stats['success_count']}")
    print(f"❌ 处理失败: {stats['error_count']}")
    print(f"📈 成功率: {stats['success_count']/stats['total_files']*100:.1f}%")
    print()
    if stats['success_count'] > 0:
        print("合理性判断分布:")
        print(f"  ✅ 合理: {stats['reasonable_count']} 个 ({stats['reasonable_count']/stats['success_count']*100:.1f}%)")
        print(f"  ❌ 不合理: {stats['unreasonable_count']} 个 ({stats['unreasonable_count']/stats['success_count']*100:.1f}%)")
    print()
    print(f"📄 汇总报告: {report_file}")
    print(f"📋 详细数据: {summary_file}")

def main():
    """主函数"""
    print("开始第一阶段完整测试...")
    print("请确保已激活tradingagents虚拟环境")
    print()
    
    try:
        success = test_stage1_all_files()
        if success:
            print("\n🎉 第一阶段测试成功完成！")
        else:
            print("\n❌ 第一阶段测试失败")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Excel文件结构
"""

import pandas as pd
import os

def check_excel_structure():
    """检查Excel文件的结构"""
    excel_file = "审核问答对（100-201）.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"Excel文件不存在: {excel_file}")
        return
    
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file)
        
        print("Excel文件结构信息:")
        print("=" * 50)
        print(f"文件名: {excel_file}")
        print(f"数据行数: {len(df)}")
        print(f"列数: {len(df.columns)}")
        print(f"列名: {list(df.columns)}")
        print()
        
        print("前5行数据预览:")
        print("-" * 50)
        print(df.head())
        print()
        
        print("数据类型:")
        print("-" * 50)
        print(df.dtypes)
        print()
        
        print("各列非空值统计:")
        print("-" * 50)
        print(df.count())
        print()
        
        # 检查优化后的回答列
        if '优化后的回答' in df.columns:
            optimized_count = df['优化后的回答'].notna().sum()
            print(f"有优化后回答的行数: {optimized_count}")
            print(f"没有优化后回答的行数: {len(df) - optimized_count}")
        
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")

if __name__ == "__main__":
    check_excel_structure()

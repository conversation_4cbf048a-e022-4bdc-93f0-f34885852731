#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证修复效果
"""

import json
import os
from typing import List, Dict, Any

def verify_stage1_failure_reasons():
    """验证第一阶段失败原因是否已修复"""
    print("=" * 60)
    print("验证第一阶段失败原因修复效果")
    print("=" * 60)
    
    results_folder = "多阶段合规审查结果/最终合规结果"
    
    if not os.path.exists(results_folder):
        print("结果文件夹不存在")
        return False
    
    stage1_failures = []
    problematic_reasons = []
    
    # 检查所有第一阶段失败的文件
    for filename in sorted(os.listdir(results_folder)):
        if filename.endswith('_result.json'):
            filepath = os.path.join(results_folder, filename)
            
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    result_data = json.load(f)
                
                if result_data.get('不合规阶段') == '第一阶段':
                    stage1_failures.append(filename)
                    
                    # 检查理由是否还有问题
                    reason = result_data.get('合规评估结果', '')
                    if '实际问题' in reason and '客户关于' in reason:
                        problematic_reasons.append({
                            'file': filename,
                            'reason': reason.split('第一阶段合理性检查不通过: ')[1] if '第一阶段合理性检查不通过: ' in reason else reason
                        })
                        
            except Exception as e:
                print(f"处理文件 {filename} 时出错: {e}")
    
    print(f"第一阶段失败文件总数: {len(stage1_failures)}")
    print(f"仍有问题的理由数量: {len(problematic_reasons)}")
    
    if problematic_reasons:
        print("\n仍有问题的文件:")
        for item in problematic_reasons:
            print(f"- {item['file']}: {item['reason'][:80]}...")
        return False
    else:
        print("✓ 所有第一阶段失败原因都已正确修复")
        return True

def verify_non_compliance_stage_fields():
    """验证不合规阶段字段是否完整"""
    print("=" * 60)
    print("验证不合规阶段字段完整性")
    print("=" * 60)
    
    results_folder = "多阶段合规审查结果/最终合规结果"
    
    if not os.path.exists(results_folder):
        print("结果文件夹不存在")
        return False
    
    non_compliant_files = []
    missing_stage_files = []
    
    # 检查所有不合规文件
    for filename in sorted(os.listdir(results_folder)):
        if filename.endswith('_result.json'):
            filepath = os.path.join(results_folder, filename)
            
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    result_data = json.load(f)
                
                # 检查是否判断为不合规
                compliance_result = result_data.get('合规评估结果', '')
                if '不合规' in compliance_result:
                    non_compliant_files.append(filename)
                    
                    # 检查是否有不合规阶段字段
                    if result_data.get('不合规阶段') is None:
                        missing_stage_files.append(filename)
                        
            except Exception as e:
                print(f"处理文件 {filename} 时出错: {e}")
    
    print(f"不合规文件总数: {len(non_compliant_files)}")
    print(f"缺少不合规阶段字段的文件数: {len(missing_stage_files)}")
    
    if missing_stage_files:
        print("\n缺少不合规阶段字段的文件:")
        for filename in missing_stage_files[:5]:  # 只显示前5个
            print(f"- {filename}")
        if len(missing_stage_files) > 5:
            print(f"... 还有 {len(missing_stage_files) - 5} 个文件")
        return False
    else:
        print("✓ 所有不合规文件都有不合规阶段字段")
        return True

def show_sample_results():
    """显示修复后的样例结果"""
    print("=" * 60)
    print("修复后的样例结果")
    print("=" * 60)
    
    results_folder = "多阶段合规审查结果/最终合规结果"
    
    # 显示001的修复结果
    sample_file = os.path.join(results_folder, "001_result.json")
    if os.path.exists(sample_file):
        try:
            with open(sample_file, 'r', encoding='utf-8') as f:
                result_data = json.load(f)
            
            print("文件: 001_result.json")
            print(f"问题: {result_data['原始数据']['问题']}")
            print(f"回答: {result_data['原始数据']['回答']}")
            print(f"不合规阶段: {result_data.get('不合规阶段')}")
            print("修复后的判断理由:")
            reason = result_data['合规评估结果'].split('第一阶段合理性检查不通过: ')[1] if '第一阶段合理性检查不通过: ' in result_data['合规评估结果'] else result_data['合规评估结果']
            print(f"  {reason}")
            
        except Exception as e:
            print(f"读取样例文件失败: {e}")
    else:
        print("样例文件不存在")

def generate_final_statistics():
    """生成最终统计信息"""
    print("=" * 60)
    print("最终统计信息")
    print("=" * 60)
    
    results_folder = "多阶段合规审查结果/最终合规结果"
    
    if not os.path.exists(results_folder):
        print("结果文件夹不存在")
        return
    
    stats = {
        'total': 0,
        'compliant': 0,
        'non_compliant': 0,
        'stage1_failures': 0,
        'stage2_failures': 0,
        'stage3_failures': 0,
        'processing_errors': 0,
        'files_with_stage_info': 0
    }
    
    for filename in sorted(os.listdir(results_folder)):
        if filename.endswith('_result.json'):
            filepath = os.path.join(results_folder, filename)
            
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    result_data = json.load(f)
                
                stats['total'] += 1
                
                # 统计合规性
                compliance_result = result_data.get('合规评估结果', '')
                if '不合规' in compliance_result:
                    stats['non_compliant'] += 1
                else:
                    stats['compliant'] += 1
                
                # 统计不合规阶段
                stage = result_data.get('不合规阶段')
                if stage:
                    stats['files_with_stage_info'] += 1
                    if stage == '第一阶段':
                        stats['stage1_failures'] += 1
                    elif stage == '第二阶段':
                        stats['stage2_failures'] += 1
                    elif stage == '第三阶段':
                        stats['stage3_failures'] += 1
                    elif stage == '处理异常':
                        stats['processing_errors'] += 1
                        
            except Exception as e:
                print(f"处理文件 {filename} 时出错: {e}")
    
    # 输出统计信息
    print(f"总文件数: {stats['total']}")
    print(f"合规文件数: {stats['compliant']} ({stats['compliant']/stats['total']*100:.1f}%)")
    print(f"不合规文件数: {stats['non_compliant']} ({stats['non_compliant']/stats['total']*100:.1f}%)")
    print()
    print("不合规阶段分布:")
    print(f"- 第一阶段失败: {stats['stage1_failures']}")
    print(f"- 第二阶段失败: {stats['stage2_failures']}")
    print(f"- 第三阶段失败: {stats['stage3_failures']}")
    print(f"- 处理异常: {stats['processing_errors']}")
    print()
    print(f"有不合规阶段信息的文件: {stats['files_with_stage_info']}/{stats['non_compliant']}")
    print(f"第一阶段通过率: {((stats['total'] - stats['stage1_failures']) / stats['total'] * 100):.1f}%")

def main():
    """主验证函数"""
    print("验证修复效果")
    print("=" * 60)
    
    # 验证问题1：第一阶段失败原因
    problem1_fixed = verify_stage1_failure_reasons()
    
    # 验证问题2：不合规阶段字段
    problem2_fixed = verify_non_compliance_stage_fields()
    
    # 显示样例结果
    show_sample_results()
    
    # 生成最终统计
    generate_final_statistics()
    
    # 总结
    print("=" * 60)
    print("修复效果总结")
    print("=" * 60)
    
    if problem1_fixed and problem2_fixed:
        print("✓ 所有问题都已成功修复！")
        print("✓ 第一阶段失败原因现在显示正确的不合规理由")
        print("✓ 所有不合规文件都有对应的不合规阶段信息")
    else:
        print("⚠ 仍有问题需要解决:")
        if not problem1_fixed:
            print("  - 第一阶段失败原因仍有问题")
        if not problem2_fixed:
            print("  - 部分不合规文件缺少不合规阶段字段")
    
    print("\n验证完成！")

if __name__ == "__main__":
    main()

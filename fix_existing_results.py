#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复现有结果文件中的问题
"""

import json
import os
import re
from typing import Dict, Any, List
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def extract_stage1_failure_reason_from_file(stage1_file_path: str) -> str:
    """从第一阶段结果文件中提取真正的不合规理由"""
    try:
        with open(stage1_file_path, 'r', encoding='utf-8') as f:
            stage1_data = json.load(f)
        
        raw_response = stage1_data.get('raw_response', '')
        reasoning = stage1_data.get('reasoning', '')
        reasonableness = stage1_data.get('reasonableness_judgment', '')
        
        # 查找JSON代码块之后的文本
        json_pattern = r'```json.*?```'
        match = re.search(json_pattern, raw_response, re.DOTALL)
        
        if match:
            # 获取JSON代码块之后的文本
            after_json = raw_response[match.end():].strip()
            if after_json:
                # 清理文本，移除多余的换行和空格
                cleaned_reason = re.sub(r'\s+', ' ', after_json).strip()
                if cleaned_reason and len(cleaned_reason) > 10:  # 确保有实质内容
                    return cleaned_reason
        
        # 如果reasoning字段描述的是问题的合理性而不是不合规的原因，
        # 说明AI的回答有问题，我们需要提供一个通用的说明
        if reasonableness == "合理" and "实际问题" in reasoning:
            return "虽然问题本身合理，但客服回答存在合规风险或不当引导，未通过第一阶段审核。"
        
        # 否则使用原始的reasoning
        return reasoning if reasoning else "未提供具体理由"
        
    except Exception as e:
        logging.error(f"提取第一阶段失败原因时出错: {e}")
        return "未提供具体理由"

def fix_stage1_failure_reasons():
    """修复第一阶段失败原因的问题"""
    print("=" * 60)
    print("修复第一阶段失败原因问题")
    print("=" * 60)
    
    results_folder = "多阶段合规审查结果/最终合规结果"
    stage1_folder = "中间结果/第一阶段_合理性检查"
    
    if not os.path.exists(results_folder) or not os.path.exists(stage1_folder):
        print("必要的文件夹不存在")
        return
    
    fixed_count = 0
    total_stage1_failures = 0
    
    # 遍历所有结果文件
    for filename in sorted(os.listdir(results_folder)):
        if filename.endswith('_result.json'):
            filepath = os.path.join(results_folder, filename)
            
            try:
                # 读取结果文件
                with open(filepath, 'r', encoding='utf-8') as f:
                    result_data = json.load(f)
                
                # 检查是否是第一阶段失败的情况
                if result_data.get('不合规阶段') == '第一阶段':
                    total_stage1_failures += 1
                    
                    # 检查是否存在问题（理由包含"实际问题"等描述合理性的内容）
                    current_reason = result_data.get('合规评估结果', '')
                    if '实际问题' in current_reason and '客户关于' in current_reason:
                        # 提取文件ID
                        file_id = filename.replace('_result.json', '')
                        stage1_file = os.path.join(stage1_folder, f"{file_id}_stage1_result.json")
                        
                        if os.path.exists(stage1_file):
                            # 从第一阶段文件中提取正确的理由
                            correct_reason = extract_stage1_failure_reason_from_file(stage1_file)
                            
                            # 更新结果文件
                            result_data['合规评估结果'] = f"**最终合规判断：** 不合规\n\n**判断理由：**\n第一阶段合理性检查不通过: {correct_reason}"
                            
                            # 保存修复后的文件
                            with open(filepath, 'w', encoding='utf-8') as f:
                                json.dump(result_data, f, ensure_ascii=False, indent=2)
                            
                            fixed_count += 1
                            print(f"修复文件: {filename}")
                            print(f"新理由: {correct_reason[:100]}...")
                            print("-" * 40)
                        else:
                            print(f"警告: 找不到对应的第一阶段文件: {stage1_file}")
                            
            except Exception as e:
                print(f"处理文件 {filename} 时出错: {e}")
    
    print(f"\n修复完成:")
    print(f"第一阶段失败总数: {total_stage1_failures}")
    print(f"修复文件数量: {fixed_count}")

def check_missing_non_compliance_stage():
    """检查缺少不合规阶段字段的问题"""
    print("=" * 60)
    print("检查缺少不合规阶段字段的问题")
    print("=" * 60)
    
    results_folder = "多阶段合规审查结果/最终合规结果"
    
    if not os.path.exists(results_folder):
        print("结果文件夹不存在")
        return
    
    missing_stage_files = []
    total_non_compliant = 0
    
    # 遍历所有结果文件
    for filename in sorted(os.listdir(results_folder)):
        if filename.endswith('_result.json'):
            filepath = os.path.join(results_folder, filename)
            
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    result_data = json.load(f)
                
                # 检查是否判断为不合规
                compliance_result = result_data.get('合规评估结果', '')
                if '不合规' in compliance_result:
                    total_non_compliant += 1
                    
                    # 检查是否缺少不合规阶段字段
                    if result_data.get('不合规阶段') is None:
                        missing_stage_files.append(filename)
                        
            except Exception as e:
                print(f"处理文件 {filename} 时出错: {e}")
    
    print(f"不合规文件总数: {total_non_compliant}")
    print(f"缺少不合规阶段字段的文件数: {len(missing_stage_files)}")
    
    if missing_stage_files:
        print("\n缺少不合规阶段字段的文件:")
        for filename in missing_stage_files[:10]:  # 只显示前10个
            print(f"- {filename}")
        if len(missing_stage_files) > 10:
            print(f"... 还有 {len(missing_stage_files) - 10} 个文件")

def generate_summary_report():
    """生成修复后的汇总报告"""
    print("=" * 60)
    print("生成修复后的汇总报告")
    print("=" * 60)
    
    results_folder = "多阶段合规审查结果/最终合规结果"
    
    if not os.path.exists(results_folder):
        print("结果文件夹不存在")
        return
    
    stats = {
        'total_files': 0,
        'stage1_failures': 0,
        'stage2_failures': 0,
        'stage3_failures': 0,
        'compliant': 0,
        'non_compliant': 0,
        'processing_errors': 0
    }
    
    # 统计各种情况
    for filename in sorted(os.listdir(results_folder)):
        if filename.endswith('_result.json'):
            filepath = os.path.join(results_folder, filename)
            
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    result_data = json.load(f)
                
                stats['total_files'] += 1
                
                # 统计不合规阶段
                non_compliance_stage = result_data.get('不合规阶段')
                if non_compliance_stage == '第一阶段':
                    stats['stage1_failures'] += 1
                elif non_compliance_stage == '第二阶段':
                    stats['stage2_failures'] += 1
                elif non_compliance_stage == '第三阶段':
                    stats['stage3_failures'] += 1
                elif non_compliance_stage == '处理异常':
                    stats['processing_errors'] += 1
                
                # 统计合规性
                compliance_result = result_data.get('合规评估结果', '')
                if '不合规' in compliance_result:
                    stats['non_compliant'] += 1
                else:
                    stats['compliant'] += 1
                    
            except Exception as e:
                print(f"处理文件 {filename} 时出错: {e}")
    
    # 输出报告
    print(f"总文件数: {stats['total_files']}")
    print(f"合规文件数: {stats['compliant']}")
    print(f"不合规文件数: {stats['non_compliant']}")
    print()
    print("不合规阶段分布:")
    print(f"- 第一阶段失败: {stats['stage1_failures']}")
    print(f"- 第二阶段失败: {stats['stage2_failures']}")
    print(f"- 第三阶段失败: {stats['stage3_failures']}")
    print(f"- 处理异常: {stats['processing_errors']}")
    print()
    print(f"第一阶段通过率: {((stats['total_files'] - stats['stage1_failures']) / stats['total_files'] * 100):.1f}%")
    print(f"整体合规率: {(stats['compliant'] / stats['total_files'] * 100):.1f}%")

def main():
    """主函数"""
    print("修复现有结果文件中的问题")
    print("=" * 60)
    
    # 1. 修复第一阶段失败原因问题
    fix_stage1_failure_reasons()
    
    # 2. 检查缺少不合规阶段字段的问题
    check_missing_non_compliance_stage()
    
    # 3. 生成修复后的汇总报告
    generate_summary_report()
    
    print("\n修复完成！")

if __name__ == "__main__":
    main()

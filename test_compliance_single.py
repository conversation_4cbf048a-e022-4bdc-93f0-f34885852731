#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单个文件合规评估测试脚本
用于测试合规评估功能
"""

import json
import os
from compliance_evaluator import ComplianceEvaluator
from datetime import datetime

def test_single_file():
    """测试单个文件的合规评估"""
    
    # API配置
    API_KEY = "sk-XrH4scuZIrUNZ7FRMMhu4Dt86sEhLEMSz2GTiSPt2TAIaZm4"
    BASE_URL = "https://api.qingyuntop.top/v1"
    
    # 创建评估器
    evaluator = ComplianceEvaluator(API_KEY, BASE_URL)
    
    # 测试文件路径
    test_file = "问答对100-201/100.json"  # 选择一个可用的文件进行测试
    
    print("=" * 60)
    print("银行客服问答对合规评估 - 单文件测试")
    print("=" * 60)
    print(f"测试文件: {test_file}")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 创建输出目录
    output_folder = "evaluation_results"

    # 处理单个文件（启用一对一保存）
    result = evaluator.process_single_file(test_file, save_individual=True, output_folder=output_folder)

    if result:
        print("原始问答对数据:")
        print("-" * 40)
        original_data = result["原始数据"]
        print(f"类别: {original_data.get('类别', 'N/A')}")
        print(f"问题: {original_data.get('问题', 'N/A')}")
        print(f"回答: {original_data.get('回答', 'N/A')}")
        print(f"分类: {original_data.get('分类', 'N/A')}")
        print(f"是否可用: {original_data.get('是否可用', 'N/A')}")
        print()

        print("合规评估结果:")
        print("-" * 40)
        print(result["合规评估结果"])
        print()

        # 生成对应的结果文件名
        original_filename = os.path.basename(test_file)
        filename_without_ext = os.path.splitext(original_filename)[0]
        individual_result_file = os.path.join(output_folder, f"{filename_without_ext}_result.json")

        print(f"评估结果已保存到: {individual_result_file}")

        # 额外保存一个带时间戳的测试结果文件（用于测试目的）
        test_output_file = f"test_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(test_output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)

        print(f"测试结果副本已保存到: {test_output_file}")

    else:
        print("测试失败：无法处理指定文件")

if __name__ == "__main__":
    test_single_file()

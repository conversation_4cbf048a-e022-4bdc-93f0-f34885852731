#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第二阶段完整测试脚本：问题类型分类
测试所有问答对100-201文件夹内的文件
"""

import json
import os
import time
from datetime import datetime
from multi_stage_compliance_evaluator import MultiStageComplianceEvaluator
from multi_stage_config import QUESTION_TYPES
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('stage2_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def test_stage2_all_files():
    """测试第二阶段：基于语义理解的问题类型分类"""
    print("=" * 80)
    print("第二阶段完整测试：基于语义理解的问题类型分类")
    print("=" * 80)

    # 创建评估器
    evaluator = MultiStageComplianceEvaluator()

    # 输入和输出目录
    input_folder = "问答对100-201"
    output_folder = "第二阶段语义分类测试结果"
    os.makedirs(output_folder, exist_ok=True)

    # 获取所有JSON文件
    if not os.path.exists(input_folder):
        print(f"❌ 输入文件夹不存在: {input_folder}")
        return False

    json_files = [f for f in os.listdir(input_folder) if f.endswith('.json')]
    json_files.sort()

    print(f"📁 找到 {len(json_files)} 个JSON文件")
    print(f"📂 输出目录: {output_folder}")
    print()

    # 显示问题类型定义（基于语义特征）
    print("问题类型定义（基于语义理解）:")
    for type_key, type_info in QUESTION_TYPES.items():
        print(f"  {type_key}: {type_info['name']}")
        print(f"    描述: {type_info['description']}")
        print(f"    语义特征: {'; '.join(type_info['semantic_indicators'][:2])}...")
    print()

    # 统计信息
    total_files = len(json_files)
    success_count = 0
    error_count = 0
    type_distribution = {}

    all_results = []

    # 处理每个文件
    for i, filename in enumerate(json_files, 1):
        file_path = os.path.join(input_folder, filename)
        file_id = os.path.splitext(filename)[0]

        print(f"🔄 处理 {i}/{total_files}: {filename}")

        try:
            # 加载问答对数据
            qa_data = evaluator.load_qa_file(file_path)
            if qa_data is None:
                print(f"   ❌ 文件加载失败")
                error_count += 1
                continue

            # 显示问题内容
            question = qa_data.get("问题", "")
            answer = qa_data.get("回答", "")
            category = qa_data.get("类别", "")
            print(f"   📝 类别: {category}")
            print(f"   📝 问题: {question}")
            print(f"   📝 回答: {answer[:100]}{'...' if len(answer) > 100 else ''}")

            # 执行第二阶段语义分类测试
            stage2_result = evaluator.stage2_question_classification(qa_data)

            if stage2_result.get("error"):
                print(f"   ❌ API调用失败: {stage2_result['error']}")
                error_count += 1
                continue

            # 提取关键结果
            question_type = stage2_result.get("question_type", "未知")
            type_name = stage2_result.get("type_name", "未知")
            reasoning = stage2_result.get("classification_reasoning", "无理由")
            result_status = stage2_result.get("stage2_result", "未知")

            print(f"   ✅ 问题类型: {question_type}")
            print(f"   📋 类型名称: {type_name}")
            print(f"   🔄 阶段结果: {result_status}")
            print(f"   💭 分类理由: {reasoning[:80]}{'...' if len(reasoning) > 80 else ''}")

            # 统计
            success_count += 1
            type_distribution[question_type] = type_distribution.get(question_type, 0) + 1

            # 构建完整结果
            complete_result = {
                "文件名": filename,
                "文件ID": file_id,
                "原始数据": qa_data,
                "第二阶段结果": {
                    "question_type": question_type,
                    "type_name": type_name,
                    "classification_reasoning": reasoning,
                    "stage2_result": result_status,
                    "raw_response": stage2_result.get("raw_response", ""),
                    "stage": "stage2_classification"
                },
                "测试时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "测试状态": "成功",
                "分类方法": "语义理解"
            }

            # 保存单个文件结果
            output_filename = f"{file_id}_stage2_result.json"
            output_filepath = os.path.join(output_folder, output_filename)

            with open(output_filepath, 'w', encoding='utf-8') as f:
                json.dump(complete_result, f, ensure_ascii=False, indent=2)

            all_results.append(complete_result)

            # 添加延迟避免API限制
            time.sleep(1)

        except Exception as e:
            print(f"   ❌ 处理失败: {e}")
            error_count += 1
            logging.error(f"处理文件 {filename} 时出错: {e}")
            continue

        print()

    # 生成汇总报告
    generate_stage2_summary(all_results, output_folder, {
        'total_files': total_files,
        'success_count': success_count,
        'error_count': error_count,
        'type_distribution': type_distribution
    })

    return True

def generate_stage2_summary(results, output_folder, stats):
    """生成第二阶段汇总报告"""

    # 保存汇总JSON
    summary_file = os.path.join(output_folder, f"stage2_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    summary_data = {
        "测试时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "统计信息": stats,
        "问题类型定义": QUESTION_TYPES,
        "详细结果": results
    }

    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(summary_data, f, ensure_ascii=False, indent=2)

    # 生成文本报告
    report_file = os.path.join(output_folder, f"stage2_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt")

    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("=" * 80 + "\n")
        f.write("第二阶段测试报告：基于语义理解的问题类型分类\n")
        f.write("=" * 80 + "\n\n")

        f.write(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"总文件数: {stats['total_files']}\n")
        f.write(f"成功处理: {stats['success_count']}\n")
        f.write(f"处理失败: {stats['error_count']}\n")
        f.write(f"成功率: {stats['success_count']/stats['total_files']*100:.1f}%\n\n")

        f.write("问题类型分布:\n")
        f.write("-" * 40 + "\n")
        for q_type, count in sorted(stats['type_distribution'].items()):
            type_name = QUESTION_TYPES.get(q_type, {}).get('name', q_type)
            percentage = count / stats['success_count'] * 100 if stats['success_count'] > 0 else 0
            f.write(f"{q_type} ({type_name}): {count} 个 ({percentage:.1f}%)\n")
        f.write("\n")


        f.write(f"\n详细结果已保存到: {summary_file}\n")

    print("=" * 80)
    print("第二阶段测试完成")
    print("=" * 80)
    print(f"📊 总文件数: {stats['total_files']}")
    print(f"✅ 成功处理: {stats['success_count']}")
    print(f"❌ 处理失败: {stats['error_count']}")
    print(f"📈 成功率: {stats['success_count']/stats['total_files']*100:.1f}%")
    print()
    print("问题类型分布:")
    for q_type, count in sorted(stats['type_distribution'].items()):
        type_name = QUESTION_TYPES.get(q_type, {}).get('name', q_type)
        percentage = count / stats['success_count'] * 100 if stats['success_count'] > 0 else 0
        print(f"  {q_type} ({type_name}): {count} 个 ({percentage:.1f}%)")
    print()
    print(f"📄 汇总报告: {report_file}")
    print(f"📋 详细数据: {summary_file}")

def test_semantic_classification_sample():
    """测试语义分类功能的示例"""
    print("=" * 80)
    print("语义分类功能示例测试")
    print("=" * 80)

    # 创建评估器
    evaluator = MultiStageComplianceEvaluator()

    # 测试样例
    test_samples = [
        {
            "问题": "什么是微粒贷？",
            "回答": "微粒贷是微众银行推出的小额信贷产品...",
            "类别": "产品咨询",
            "期望类型": "DEFINITION"
        },
        {
            "问题": "如何申请微粒贷？",
            "回答": "申请微粒贷需要以下步骤：1.下载微众银行APP...",
            "类别": "操作指导",
            "期望类型": "OPERATION"
        },
        {
            "问题": "我可以提前还款吗？",
            "回答": "是的，微粒贷支持提前还款...",
            "类别": "还款咨询",
            "期望类型": "YES_NO"
        }
    ]

    print("测试语义分类准确性:")
    print("-" * 50)

    for i, sample in enumerate(test_samples, 1):
        print(f"\n样例 {i}:")
        print(f"问题: {sample['问题']}")
        print(f"期望类型: {sample['期望类型']}")

        result = evaluator.stage2_question_classification(sample)

        if result.get("error"):
            print(f"❌ 分类失败: {result['error']}")
            continue

        actual_type = result.get("question_type", "未知")
        confidence = result.get("confidence_score", 0)
        reasoning = result.get("classification_reasoning", "")
        semantic_analysis = result.get("semantic_analysis", "")

        is_correct = actual_type == sample['期望类型']
        status = "✅ 正确" if is_correct else "❌ 错误"

        print(f"实际类型: {actual_type} {status}")
        print(f"分类理由: {reasoning}")

        time.sleep(1)  # 避免API限制

def main():
    """主函数"""
    print("开始第二阶段语义分类测试...")
    print("请确保已激活tradingagents虚拟环境")
    print()

    # 选择测试模式
    print("请选择测试模式:")
    print("1. 完整测试（所有文件）")
    print("2. 语义分类示例测试")

    try:
        choice = input("请输入选择 (1 或 2): ").strip()

        if choice == "1":
            success = test_stage2_all_files()
            if success:
                print("\n🎉 第二阶段完整测试成功完成！")
            else:
                print("\n❌ 第二阶段完整测试失败")
        elif choice == "2":
            test_semantic_classification_sample()
            print("\n🎉 语义分类示例测试完成！")
        else:
            print("无效选择，执行完整测试...")
            success = test_stage2_all_files()
            if success:
                print("\n🎉 第二阶段测试成功完成！")
            else:
                print("\n❌ 第二阶段测试失败")

    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

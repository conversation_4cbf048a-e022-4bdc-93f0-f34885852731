#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的脚本
"""

import os
import json
import shutil

def test_optimized_conversion():
    """测试优化后的转换脚本"""
    print("测试优化后的转换脚本")
    print("=" * 50)
    
    # 备份原有文件夹
    original_folder = "问答对100-201(优化后)"
    backup_folder = "问答对100-201(优化后)_backup"
    
    if os.path.exists(original_folder):
        if os.path.exists(backup_folder):
            shutil.rmtree(backup_folder)
        shutil.copytree(original_folder, backup_folder)
        print(f"已备份原文件夹到: {backup_folder}")
    
    # 运行优化后的脚本
    print("\n运行优化后的脚本...")
    os.system("python excel_to_json_optimized.py")
    
    # 验证结果
    print("\n验证优化效果:")
    print("-" * 30)
    
    if not os.path.exists(original_folder):
        print("转换失败，文件夹不存在")
        return
    
    # 检查几个有优化后回答的文件
    test_files = ["002.json", "004.json", "005.json"]  # 这些文件有优化后的回答
    
    for filename in test_files:
        filepath = os.path.join(original_folder, filename)
        if os.path.exists(filepath):
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                has_optimized = data.get('优化后的回答') is not None
                reason = data.get('原因')
                
                print(f"\n文件: {filename}")
                print(f"问题: {data.get('问题', 'N/A')[:40]}...")
                print(f"有优化后回答: {'是' if has_optimized else '否'}")
                print(f"原因字段: {reason}")
                
                # 验证逻辑：如果有优化后回答，原因应该为null
                if has_optimized and reason is not None:
                    print(f"❌ 错误：有优化后回答但原因不为null")
                elif has_optimized and reason is None:
                    print(f"✅ 正确：有优化后回答且原因为null")
                elif not has_optimized:
                    print(f"ℹ️  信息：无优化后回答，原因保持原值")
                    
            except Exception as e:
                print(f"读取文件 {filename} 时出错: {e}")
        else:
            print(f"文件 {filename} 不存在")
    
    # 检查几个没有优化后回答的文件
    print(f"\n检查没有优化后回答的文件:")
    print("-" * 30)
    
    test_files_no_opt = ["001.json", "003.json", "008.json"]  # 这些文件没有优化后的回答
    
    for filename in test_files_no_opt:
        filepath = os.path.join(original_folder, filename)
        if os.path.exists(filepath):
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                has_optimized = data.get('优化后的回答') is not None
                reason = data.get('原因')
                
                print(f"\n文件: {filename}")
                print(f"问题: {data.get('问题', 'N/A')[:40]}...")
                print(f"有优化后回答: {'是' if has_optimized else '否'}")
                print(f"原因字段: {reason}")
                
                if not has_optimized:
                    print(f"✅ 正确：无优化后回答，原因字段保持原值")
                    
            except Exception as e:
                print(f"读取文件 {filename} 时出错: {e}")

def compare_before_after():
    """比较优化前后的差异"""
    print(f"\n比较优化前后的差异:")
    print("=" * 50)
    
    original_folder = "问答对100-201(优化后)"
    backup_folder = "问答对100-201(优化后)_backup"
    
    if not os.path.exists(backup_folder):
        print("没有备份文件夹，无法比较")
        return
    
    # 比较有优化后回答的文件
    test_files = ["002.json", "004.json", "005.json"]
    
    for filename in test_files:
        original_path = os.path.join(original_folder, filename)
        backup_path = os.path.join(backup_folder, filename)
        
        if os.path.exists(original_path) and os.path.exists(backup_path):
            try:
                with open(original_path, 'r', encoding='utf-8') as f:
                    new_data = json.load(f)
                with open(backup_path, 'r', encoding='utf-8') as f:
                    old_data = json.load(f)
                
                print(f"\n文件: {filename}")
                print(f"优化前原因: {old_data.get('原因')}")
                print(f"优化后原因: {new_data.get('原因')}")
                
                if old_data.get('原因') != new_data.get('原因'):
                    print("✅ 原因字段已更新")
                else:
                    print("ℹ️  原因字段无变化")
                    
            except Exception as e:
                print(f"比较文件 {filename} 时出错: {e}")

if __name__ == "__main__":
    test_optimized_conversion()
    compare_before_after()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的多阶段合规评估器
"""

import json
import os
from multi_stage_compliance_evaluator import MultiStageComplianceEvaluator

def test_stage1_failure_reason_extraction():
    """测试第一阶段失败原因提取功能"""
    print("=" * 60)
    print("测试第一阶段失败原因提取功能")
    print("=" * 60)
    
    evaluator = MultiStageComplianceEvaluator()
    
    # 模拟第一阶段的结果（类似001的情况）
    stage1_result = {
        "reasonableness_judgment": "合理",
        "reasoning": "客户关于没有实际经营地址的情况是企业在注册和运营过程中可能会遇到的实际问题。",
        "stage1_result": "不通过"
    }
    
    raw_response = """```json
{
  "reasonableness_judgment": "合理",
  "reasoning": "客户关于没有实际经营地址的情况是企业在注册和运营过程中可能会遇到的实际问题。",
  "stage1_result": "不通过"
}
```

尽管客户问题合理，但客服的回答建议客户将家庭地址作为办公地址，这种做法在某些情况下可能违反了商业登记法规和银行开户政策，尤其是在需要提供真实经营地址的情况下。因此，该回答存在引导客户进行可能不符合法规的操作的风险。"""
    
    # 测试提取功能
    actual_reason = evaluator._extract_stage1_failure_reason(raw_response, stage1_result)
    
    print(f"原始reasoning: {stage1_result['reasoning']}")
    print(f"提取的实际原因: {actual_reason}")
    print()
    
    # 验证是否正确提取了JSON之后的解释
    expected_keywords = ["客服的回答", "违反", "法规", "风险"]
    found_keywords = [kw for kw in expected_keywords if kw in actual_reason]
    
    print(f"期望关键词: {expected_keywords}")
    print(f"找到关键词: {found_keywords}")
    print(f"提取成功: {len(found_keywords) >= 2}")
    print()

def test_single_qa_processing():
    """测试单个问答对的处理"""
    print("=" * 60)
    print("测试单个问答对的处理")
    print("=" * 60)
    
    evaluator = MultiStageComplianceEvaluator()
    
    # 测试数据（模拟001.json的内容）
    qa_data = {
        "类别": "票据",
        "问题": "如果我没有实际经营地址怎么办?",
        "回答": "如果没有实际经营地址，您可以填写家庭地址作为办公地址",
        "分类": None,
        "优化后的回答": None,
        "是否可用": "不可用",
        "原因": "无法定位这个问题的场景，以及回答是否合规"
    }
    
    print("测试数据:")
    print(f"问题: {qa_data['问题']}")
    print(f"回答: {qa_data['回答']}")
    print()
    
    # 处理问答对
    result = evaluator.process_single_qa(qa_data, "test_001")
    
    print("处理结果:")
    print(f"评估状态: {result['评估状态']}")
    print(f"不合规阶段: {result.get('不合规阶段', '无')}")
    print()
    print("合规评估结果:")
    print(result['合规评估结果'])
    print()

def check_existing_results():
    """检查现有结果文件中的问题"""
    print("=" * 60)
    print("检查现有结果文件中的问题")
    print("=" * 60)
    
    results_folder = "多阶段合规审查结果/最终合规结果"
    
    if not os.path.exists(results_folder):
        print("结果文件夹不存在")
        return
    
    # 检查前几个文件
    problem_files = []
    files_checked = 0
    
    for filename in sorted(os.listdir(results_folder))[:10]:  # 只检查前10个文件
        if filename.endswith('.json'):
            filepath = os.path.join(results_folder, filename)
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                files_checked += 1
                
                # 检查问题1：第一阶段不合规理由是否正常
                if data.get('不合规阶段') == '第一阶段':
                    reason = data.get('合规评估结果', '')
                    if '实际问题' in reason and '客户关于' in reason:
                        problem_files.append({
                            'file': filename,
                            'issue': '第一阶段理由异常',
                            'reason': reason.split('第一阶段合理性检查不通过: ')[1] if '第一阶段合理性检查不通过: ' in reason else reason
                        })
                
                # 检查问题2：不合规但没有不合规阶段字段
                if '不合规' in data.get('合规评估结果', '') and data.get('不合规阶段') is None:
                    problem_files.append({
                        'file': filename,
                        'issue': '缺少不合规阶段字段',
                        'reason': '判断为不合规但没有不合规阶段信息'
                    })
                    
            except Exception as e:
                print(f"读取文件 {filename} 失败: {e}")
    
    print(f"检查了 {files_checked} 个文件")
    print(f"发现问题文件 {len(problem_files)} 个:")
    print()
    
    for problem in problem_files:
        print(f"文件: {problem['file']}")
        print(f"问题: {problem['issue']}")
        print(f"详情: {problem['reason'][:100]}...")
        print("-" * 40)

def main():
    """主测试函数"""
    print("多阶段合规评估器修复测试")
    print("=" * 60)
    
    # 测试1：第一阶段失败原因提取
    test_stage1_failure_reason_extraction()
    
    # 测试2：单个问答对处理
    test_single_qa_processing()
    
    # 测试3：检查现有结果
    check_existing_results()
    
    print("测试完成！")

if __name__ == "__main__":
    main()

{
    "terminal.integrated.env.windows": {
        "PYTHONPATH": ""
    },
    "python.terminal.activateEnvironment": false,
    "python.terminal.activateEnvInCurrentTerminal": false,
    // 恢复继承系统环境变量，避免 VSCode 把 PATH 当作命令执行
    "terminal.integrated.inheritEnv": true,
    // 删除 -NoProfile，让 PowerShell 正常加载你的 profile 脚本
    "terminal.integrated.profiles.windows": {
        "PowerShell": {
            "source": "PowerShell",
            "args": [
                "-ExecutionPolicy",
                "Bypass"
            ]
        }
    },
    "terminal.integrated.defaultProfile.windows": "PowerShell",
    // 指向你实际的 Miniconda Python
    "python.defaultInterpreterPath": "D:\\ProgramData\\miniconda3\\python.exe",
    "python-envs.defaultEnvManager": "ms-python.python:conda",
    "python-envs.defaultPackageManager": "ms-python.python:conda",
    "python-envs.pythonProjects": []
}
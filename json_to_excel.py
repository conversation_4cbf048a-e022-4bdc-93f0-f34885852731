#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSON文件转Excel文件脚本
将合规审查结果的JSON文件转换为Excel汇总文件
"""

import pandas as pd
import json
import os
import re
from datetime import datetime
from pathlib import Path

def extract_compliance_judgment(evaluation_result):
    """
    从评估结果中提取最终合规判断

    Args:
        evaluation_result: 评估结果文本

    Returns:
        合规判断结果 (合规/不合规)
    """
    if not evaluation_result:
        return "未知"

    # 查找最终合规判断
    lines = evaluation_result.split('\n')
    for line in lines:
        if '**最终合规判断：**' in line:
            if '合规' in line:
                if '不合规' in line:
                    return "不合规"
                else:
                    return "合规"

    # 如果没有找到明确的判断，尝试从投票结果推断
    for line in lines:
        if '合规票数：' in line and '不合规票数：' in line:
            try:
                # 提取票数
                parts = line.split('不合规票数：')
                if len(parts) >= 2:
                    compliant_part = parts[0].replace('合规票数：', '').strip()
                    non_compliant_part = parts[1].strip()

                    compliant_votes = int(compliant_part.replace('票', ''))
                    non_compliant_votes = int(non_compliant_part.replace('票', ''))

                    if non_compliant_votes > compliant_votes:
                        return "不合规"
                    elif compliant_votes > non_compliant_votes:
                        return "合规"
                    else:
                        return "未知"
            except:
                continue

    return "未知"

def extract_ai_judgment_reason(evaluation_result):
    """
    从评估结果中提取AI判断理由

    Args:
        evaluation_result: 评估结果文本

    Returns:
        AI判断理由
    """
    if not evaluation_result:
        return ""

    # 查找判断理由部分
    lines = evaluation_result.split('\n')
    reason_text = ""

    # 寻找 **判断理由：** 部分
    for i, line in enumerate(lines):
        if '**判断理由：**' in line:
            # 提取该行的理由部分
            reason_part = line.split('**判断理由：**')
            if len(reason_part) > 1:
                reason_text = reason_part[1].strip()

            # 继续读取后续行，直到遇到下一个 ** 开头的行或空行
            for j in range(i + 1, len(lines)):
                next_line = lines[j].strip()
                if not next_line or next_line.startswith('**'):
                    break
                reason_text += " " + next_line
            break

    return reason_text.strip()

def json_to_excel(json_folder="合规审查结果（100-201）优化提示词", output_file=None):
    """
    将JSON文件转换为Excel文件

    Args:
        json_folder: JSON文件夹路径
        output_file: 输出Excel文件路径

    Returns:
        转换成功的文件数量
    """
    try:
        # 检查文件夹是否存在
        if not os.path.exists(json_folder):
            print(f"错误: 文件夹不存在 - {json_folder}")
            return 0

        # 获取所有JSON文件，只处理数字开头的result文件
        all_files = os.listdir(json_folder)
        json_files = []
        for f in all_files:
            if f.endswith('_result.json') and f[:3].isdigit():
                # 提取文件编号
                file_num = int(f[:3])
                # 跳过104-113行的内容（对应104-113号文件）
                if 104 <= file_num <= 113:
                    print(f"跳过文件: {f} (104-113范围)")
                    continue
                json_files.append(f)

        json_files.sort()  # 按文件名排序

        if not json_files:
            print(f"错误: 在 {json_folder} 中没有找到有效的JSON文件")
            return 0

        print(f"找到 {len(json_files)} 个有效JSON文件")

        # 准备数据列表
        data_list = []
        success_count = 0

        # 处理每个JSON文件
        for filename in json_files:
            try:
                filepath = os.path.join(json_folder, filename)

                # 读取JSON文件
                with open(filepath, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # 提取原始数据
                original_data = data.get('原始数据', {})

                # 提取合规判断和AI判断理由
                evaluation_result = data.get('合规评估结果', '')
                compliance_judgment = extract_compliance_judgment(evaluation_result)
                ai_judgment_reason = extract_ai_judgment_reason(evaluation_result)

                # 构建Excel行数据，按照目标Excel格式
                row_data = {
                    '类别': original_data.get('类别', ''),
                    '问题': original_data.get('问题', ''),
                    '回答': original_data.get('回答', ''),
                    '优化后的回答': original_data.get('优化后的回答', ''),
                    '原因': original_data.get('原因', ''),
                    '是否可用': original_data.get('是否可用', ''),
                    '合规评估结果': f"** {compliance_judgment}",
                    'AI判断理由': ai_judgment_reason,
                    # '存在的问题': ''  # 这一列在原Excel中为空，保持一致
                }

                data_list.append(row_data)
                success_count += 1
                print(f"已处理: {filename}")

            except Exception as e:
                print(f"处理文件 {filename} 时出错: {e}")
                continue

        if not data_list:
            print("错误: 没有成功处理任何文件")
            return 0

        # 创建DataFrame
        df = pd.DataFrame(data_list)

        # 确保列的顺序与目标Excel一致
        column_order = ['类别', '问题', '回答', '优化后的回答', '原因', '是否可用', '合规评估结果', 'AI判断理由']
        df = df[column_order]

        # 生成输出文件名
        if output_file is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f"合规审查汇总（100-201）优化提示词后_优化后回答.xlsx"

        # 保存为Excel文件
        df.to_excel(output_file, index=False, engine='openpyxl')

        print(f"\n转换完成!")
        print(f"成功处理: {success_count} 个文件")
        print(f"输出文件: {output_file}")

        # 显示统计信息
        print(f"\n统计信息:")
        if '合规评估结果' in df.columns:
            # 统计合规判断结果
            compliance_counts = {}
            for result in df['合规评估结果']:
                if '合规' in str(result):
                    if '不合规' in str(result):
                        key = "不合规"
                    else:
                        key = "合规"
                else:
                    key = "未知"
                compliance_counts[key] = compliance_counts.get(key, 0) + 1

            for judgment, count in compliance_counts.items():
                print(f"  {judgment}: {count} 个")

        return success_count

    except Exception as e:
        print(f"转换过程中出错: {e}")
        return 0

def main():
    """主函数"""
    print("JSON转Excel文件转换器")
    print("=" * 50)

    # 默认配置
    json_dir = "合规审查结果（100-201）优化提示词_优化后回答"
    output_file = None  # 自动生成文件名

    # 检查JSON文件夹是否存在
    if not os.path.exists(json_dir):
        print(f"错误: JSON文件夹不存在 - {json_dir}")
        print("请确保文件夹存在于当前目录中")
        return

    # 执行转换
    result = json_to_excel(json_dir, output_file)

    if result > 0:
        print(f"\n✓ 转换成功! 共处理 {result} 个JSON文件")
    else:
        print("\n✗ 转换失败!")

if __name__ == "__main__":
    main()

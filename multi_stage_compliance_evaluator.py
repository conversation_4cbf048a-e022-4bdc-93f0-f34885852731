#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多步骤银行客服问答对合规评估脚本
实现三阶段合规审计流程：合理性检查 -> 问题分类 -> 合规评估
"""

import openai
import json
import os
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging
from config import API_CONFIG, PATH_CONFIG, MODEL_CONFIG
from multi_stage_config import (
    MULTI_STAGE_CONFIG, QUESTION_TYPES,
    STAGE1_REASONABLENESS_PROMPT, STAGE2_CLASSIFICATION_PROMPT,
    STAGE3_TYPE_SPECIFIC_PROMPTS,
    OUTPUT_PATHS
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('multi_stage_compliance_evaluation.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class MultiStageComplianceEvaluator:
    """多步骤合规评估器"""

    def __init__(self, api_key: str = None, base_url: str = None):
        """初始化多步骤合规评估器"""
        self.client = openai.OpenAI(
            base_url=base_url or API_CONFIG["base_url"],
            api_key=api_key or API_CONFIG["api_key"]
        )
        self.model = API_CONFIG["model"]
        self.model_config = MODEL_CONFIG
        self.multi_stage_config = MULTI_STAGE_CONFIG

        # 创建输出目录
        self._create_output_directories()

    def _create_output_directories(self):
        """创建所有必要的输出目录"""
        base_folder = self.multi_stage_config["output_folder"]
        intermediate_folder = self.multi_stage_config["intermediate_folder"]

        # 创建主要输出目录
        os.makedirs(base_folder, exist_ok=True)
        os.makedirs(intermediate_folder, exist_ok=True)

        # 创建各阶段子目录
        for folder_name in OUTPUT_PATHS.values():
            stage_path = os.path.join(intermediate_folder, folder_name)
            os.makedirs(stage_path, exist_ok=True)

    def _call_model(self, prompt: str, user_content: str, max_retries: int = 3) -> Optional[str]:
        """调用模型API，包含重试机制"""
        for attempt in range(max_retries):
            try:
                messages = [
                    {"role": "system", "content": prompt},
                    {"role": "user", "content": user_content}
                ]

                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=messages,
                    temperature=self.model_config["temperature"],
                    max_tokens=self.model_config["max_tokens"]
                )

                return response.choices[0].message.content

            except Exception as e:
                logging.warning(f"API调用失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # 指数退避
                else:
                    logging.error(f"API调用最终失败: {e}")
                    return None

    def _parse_json_response(self, response: str) -> Optional[Dict]:
        """解析JSON响应，处理可能的格式问题"""
        import re

        # 清理响应文本
        cleaned_response = response.strip()

        # 策略1：尝试直接解析
        try:
            return json.loads(cleaned_response)
        except json.JSONDecodeError:
            pass

        # 策略2：提取最外层的JSON代码块
        try:
            json_match = re.search(r'```json\s*(.*?)\s*```', cleaned_response, re.DOTALL)
            if json_match:
                json_content = json_match.group(1).strip()
                return json.loads(json_content)
        except json.JSONDecodeError:
            pass

        # 策略3：提取任何代码块（不限于json标记）
        try:
            code_block_match = re.search(r'```[a-zA-Z]*\s*(.*?)\s*```', cleaned_response, re.DOTALL)
            if code_block_match:
                json_content = code_block_match.group(1).strip()
                return json.loads(json_content)
        except json.JSONDecodeError:
            pass

        # 策略4：查找最大的完整JSON对象
        try:
            # 找到第一个{和最后一个}之间的内容
            start_idx = cleaned_response.find('{')
            end_idx = cleaned_response.rfind('}')
            if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                json_content = cleaned_response[start_idx:end_idx+1]
                return json.loads(json_content)
        except json.JSONDecodeError:
            pass

        # 策略5：尝试修复常见的JSON格式问题
        try:
            # 移除可能的前后缀文本
            lines = cleaned_response.split('\n')
            json_lines = []
            in_json = False
            brace_count = 0

            for line in lines:
                if '{' in line and not in_json:
                    in_json = True
                    json_lines.append(line)
                    brace_count += line.count('{') - line.count('}')
                elif in_json:
                    json_lines.append(line)
                    brace_count += line.count('{') - line.count('}')
                    if brace_count <= 0:
                        break

            if json_lines:
                json_content = '\n'.join(json_lines)
                return json.loads(json_content)

        except json.JSONDecodeError:
            pass

        # 记录详细的错误信息
        logging.error(f"所有JSON解析策略都失败了")
        logging.error(f"响应长度: {len(response)}")
        logging.error(f"响应前500字符: {response[:500]}")
        logging.error(f"响应后100字符: {response[-100:]}")

        return None



    def stage1_reasonableness_check(self, qa_data: Dict[str, Any]) -> Dict[str, Any]:
        """第一阶段：合理性检查"""
        logging.info("执行第一阶段：合理性检查")

        question = qa_data.get("问题", "")
        answer = qa_data.get("回答", "")

        user_content = f"""
【客户问题】: {question}
【客服回答】: {answer}

        """.strip()

        response = self._call_model(STAGE1_REASONABLENESS_PROMPT, user_content)
        if not response:
            return {"stage1_result": "失败", "error": "API调用失败"}

        parsed_result = self._parse_json_response(response)
        if not parsed_result:
            return {"stage1_result": "失败", "error": "响应解析失败", "raw_response": response}

        # 添加原始响应用于调试
        parsed_result["raw_response"] = response
        parsed_result["stage"] = "stage1_reasonableness"

        return parsed_result

    def stage2_question_classification(self, qa_data: Dict[str, Any]) -> Dict[str, Any]:
        """第二阶段：基于语义理解的问题类型分类"""
        logging.info("执行第二阶段：基于语义理解的问题类型分类")

        question = qa_data.get("问题", "")
        answer = qa_data.get("回答", "")
        category = qa_data.get("类别", "")

        user_content = f"""
【业务类别】: {category}
【客户问题】: {question}
【客服回答】: {answer}


        """.strip()

        response = self._call_model(STAGE2_CLASSIFICATION_PROMPT, user_content)
        if not response:
            return {"stage2_result": "失败", "error": "API调用失败"}

        parsed_result = self._parse_json_response(response)
        if not parsed_result:
            return {"stage2_result": "失败", "error": "响应解析失败", "raw_response": response}

        # 解析问题类型，从新格式中提取类型标识符
        question_type_raw = parsed_result.get("question_type", "")

        # 映射新格式的问题类型到内部标识符
        type_mapping = {
            "a 定义/概念解释类": "DEFINITION",
            "b 操作类": "OPERATION",
            "c 审核失败/资料补充类": "AUDIT_FAILURE",
            "d 是否类问题": "YES_NO",
            "e 事实性/趋势性问题": "FACTUAL",
            "f 其他类": "OTHER"
        }

        question_type = None
        for key, value in type_mapping.items():
            if question_type_raw.startswith(key):
                question_type = value
                break

        if not question_type or question_type not in QUESTION_TYPES:
            logging.warning(f"无效的问题类型: {question_type_raw}")
            question_type = "OTHER"  # 默认为其他类问题

        # 更新解析结果
        parsed_result["question_type"] = question_type
        parsed_result["question_type_raw"] = question_type_raw
        parsed_result["classification_method"] = "semantic_understanding"
        parsed_result["raw_response"] = response
        parsed_result["stage"] = "stage2_classification"

        # 记录分类详情
        logging.info(f"问题分类结果: {question_type} ({question_type_raw})")

        return parsed_result

    def stage3_type_specific_evaluation(self, qa_data: Dict[str, Any], question_type: str) -> Dict[str, Any]:
        """第三阶段：特定类型合规评估"""
        logging.info(f"执行第三阶段：{question_type}类型合规评估")

        if question_type not in STAGE3_TYPE_SPECIFIC_PROMPTS:
            logging.error(f"未找到问题类型 {question_type} 的评估提示词")
            return {"stage3_result": "失败", "error": f"未支持的问题类型: {question_type}"}

        question = qa_data.get("问题", "")
        answer = qa_data.get("回答", "")
        category = qa_data.get("类别", "")

        user_content = f"""
【业务类别】: {category}
【问题类型】: {QUESTION_TYPES[question_type]['name']}
【客户问题】: {question}
【客服回答】: {answer}

请根据该问题类型的特定标准进行合规评估。
        """.strip()

        prompt = STAGE3_TYPE_SPECIFIC_PROMPTS[question_type]
        response = self._call_model(prompt, user_content)

        if not response:
            return {"stage3_result": "失败", "error": "API调用失败"}

        parsed_result = self._parse_json_response(response)
        if not parsed_result:
            return {"stage3_result": "失败", "error": "响应解析失败", "raw_response": response}

        parsed_result["raw_response"] = response
        parsed_result["stage"] = "stage3_type_specific"
        parsed_result["evaluated_type"] = question_type

        return parsed_result



    def _save_stage_result(self, stage_name: str, file_id: str, result: Dict[str, Any]):
        """保存阶段结果到文件"""
        if not self.multi_stage_config["save_intermediate_results"]:
            return

        try:
            stage_folder = OUTPUT_PATHS.get(f"{stage_name}_folder", stage_name)
            intermediate_folder = self.multi_stage_config["intermediate_folder"]
            output_path = os.path.join(intermediate_folder, stage_folder)

            filename = f"{file_id}_{stage_name}_result.json"
            filepath = os.path.join(output_path, filename)

            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)

            logging.debug(f"阶段结果已保存: {filepath}")

        except Exception as e:
            logging.error(f"保存阶段结果失败: {e}")

    def process_single_qa(self, qa_data: Dict[str, Any], file_id: str) -> Dict[str, Any]:
        """处理单个问答对的完整三阶段流程"""
        logging.info(f"开始处理问答对: {file_id}")

        # 初始化结果结构，参考现有格式
        final_result = {
            "文件名": f"{file_id}.json",  # 如：001.json
            "原始数据": qa_data,
            "评估时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "合规评估结果": "",
            "评估状态": "进行中",
            "多阶段结果": {},
            "不合规阶段": None  # 新增：记录在哪个阶段判定为不合规
        }

        try:
            # 第一阶段：合理性检查
            stage1_result = self.stage1_reasonableness_check(qa_data)
            final_result["多阶段结果"]["stage1"] = stage1_result
            self._save_stage_result("stage1", file_id, stage1_result)

            # 检查第一阶段是否有错误
            if stage1_result.get("error"):
                final_result["合规评估结果"] = f"**最终合规判断：** 不合规\n\n**判断理由：**\n第一阶段处理失败: {stage1_result['error']}"
                final_result["评估状态"] = "第一阶段失败"
                final_result["不合规阶段"] = "第一阶段"
                return final_result

            # 检查第一阶段是否通过（主要看stage1_result）
            if stage1_result.get("stage1_result") == "不通过":
                reasoning = stage1_result.get('reasoning', '未提供理由')
                final_result["合规评估结果"] = f"**最终合规判断：** 不合规\n\n**判断理由：**\n第一阶段合理性检查不通过: {reasoning}"
                final_result["评估状态"] = "第一阶段终止"
                final_result["不合规阶段"] = "第一阶段"
                return final_result

            time.sleep(self.multi_stage_config["stage_delay"])

            # 第二阶段：问题类型分类
            stage2_result = self.stage2_question_classification(qa_data)
            final_result["多阶段结果"]["stage2"] = stage2_result
            self._save_stage_result("stage2", file_id, stage2_result)

            # 检查第二阶段是否有错误
            if stage2_result.get("error"):
                final_result["合规评估结果"] = f"**最终合规判断：** 不合规\n\n**判断理由：**\n第二阶段处理失败: {stage2_result['error']}"
                final_result["评估状态"] = "第二阶段失败"
                final_result["不合规阶段"] = "第二阶段"
                return final_result

            question_type = stage2_result.get("question_type", "OPERATION")
            time.sleep(self.multi_stage_config["stage_delay"])

            # 第三阶段：特定类型合规评估（最终阶段）
            stage3_result = self.stage3_type_specific_evaluation(qa_data, question_type)
            final_result["多阶段结果"]["stage3"] = stage3_result
            self._save_stage_result("stage3", file_id, stage3_result)

            # 检查第三阶段是否有错误
            if stage3_result.get("error"):
                final_result["合规评估结果"] = f"**最终合规判断：** 不合规\n\n**判断理由：**\n第三阶段处理失败: {stage3_result['error']}"
                final_result["评估状态"] = "第三阶段失败"
                final_result["不合规阶段"] = "第三阶段"
                return final_result

            # 从第三阶段直接提取最终结果
            compliance_judgment = stage3_result.get("compliance_judgment", "不合规")
            reason = stage3_result.get("reason", "基于第三阶段合规评估结果")

            # 构建合规评估结果，参考现有格式
            final_result["合规评估结果"] = f"**最终合规判断：** {compliance_judgment}\n\n**判断理由：**\n{reason}"

            # 如果有优化建议，添加到结果中
            if stage3_result.get("optimization_suggestions"):
                final_result["合规评估结果"] += f"\n\n**优化建议：**\n{stage3_result['optimization_suggestions']}"

            # 如果判断为不合规，记录不合规阶段
            if compliance_judgment == "不合规":
                final_result["不合规阶段"] = "第三阶段"

            # 添加评估详情到多阶段结果中
            final_result["多阶段结果"]["评估详情"] = {
                "问题类型": question_type,
                "类型名称": QUESTION_TYPES.get(question_type, {}).get('name', '未知'),
                "合规评估": stage3_result
            }

            final_result["评估状态"] = "成功"

        except Exception as e:
            logging.error(f"处理问答对 {file_id} 时出错: {e}")
            final_result["评估状态"] = "失败"
            final_result["合规评估结果"] = f"**最终合规判断：** 不合规\n\n**判断理由：**\n处理过程中出现异常: {str(e)}"
            final_result["不合规阶段"] = "处理异常"

        return final_result

    def load_qa_file(self, file_path: str) -> Dict[str, Any]:
        """加载问答对文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return data
        except Exception as e:
            logging.error(f"加载文件 {file_path} 失败: {e}")
            return None

    def process_qa_folder(self, folder_path: str, output_file: str = None) -> List[Dict[str, Any]]:
        """批量处理问答对文件夹"""
        if not os.path.exists(folder_path):
            logging.error(f"文件夹不存在: {folder_path}")
            return []

        # 获取所有JSON文件
        json_files = [f for f in os.listdir(folder_path) if f.endswith('.json')]
        json_files.sort()

        logging.info(f"找到 {len(json_files)} 个JSON文件")

        all_results = []

        for i, filename in enumerate(json_files, 1):
            file_path = os.path.join(folder_path, filename)
            # 提取文件名（不含扩展名）作为file_id，如 "001"
            file_id = os.path.splitext(filename)[0]

            try:
                # 加载问答对数据
                qa_data = self.load_qa_file(file_path)
                if qa_data is None:
                    logging.warning(f"跳过文件: {filename}")
                    continue

                # 处理问答对
                result = self.process_single_qa(qa_data, file_id)
                all_results.append(result)

                # 从合规评估结果中提取判断结果
                compliance_status = "不合规"  # 默认值
                if "合规" in result.get("合规评估结果", ""):
                    if "不合规" not in result.get("合规评估结果", ""):
                        compliance_status = "合规"

                logging.info(f"完成 {i}/{len(json_files)}: {filename} - {compliance_status}")

                # 保存单个结果文件，使用原始文件名生成结果文件名
                self._save_individual_result(result, file_id)

            except Exception as e:
                logging.error(f"处理文件 {filename} 时出错: {e}")
                continue

        # 保存汇总结果
        if output_file:
            self._save_summary_results(all_results, output_file)

        return all_results

    def _save_individual_result(self, result: Dict[str, Any], file_id: str):
        """保存单个结果文件，基于原始文件名生成结果文件名"""
        try:
            # 直接使用新的结果格式，已经匹配参考格式
            compatible_result = {
                "文件名": f"{file_id}.json",  # 确保文件名格式正确
                "原始数据": result["原始数据"],
                "评估时间": result["评估时间"],
                "合规评估结果": result["合规评估结果"],
                "评估状态": result["评估状态"]
            }

            # 如果有不合规阶段信息，添加到结果中
            if result.get("不合规阶段"):
                compatible_result["不合规阶段"] = result["不合规阶段"]

            # 保存到最终结果目录
            final_folder = OUTPUT_PATHS["final_results_folder"]
            base_folder = self.multi_stage_config["output_folder"]
            output_path = os.path.join(base_folder, final_folder)
            os.makedirs(output_path, exist_ok=True)

            # 生成结果文件名：原始文件名_result.json（如：001_result.json）
            output_filename = f"{file_id}_result.json"
            output_filepath = os.path.join(output_path, output_filename)

            with open(output_filepath, 'w', encoding='utf-8') as f:
                json.dump(compatible_result, f, ensure_ascii=False, indent=2)

            logging.info(f"结果已保存: {output_filename}")

        except Exception as e:
            logging.error(f"保存单个结果失败: {e}")

    def _save_summary_results(self, results: List[Dict[str, Any]], output_file: str):
        """保存汇总结果"""
        try:
            os.makedirs(os.path.dirname(output_file) if os.path.dirname(output_file) else '.', exist_ok=True)

            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)

            logging.info(f"汇总结果已保存到: {output_file}")

            # 生成统计报告
            self._generate_multi_stage_report(results, output_file)

        except Exception as e:
            logging.error(f"保存汇总结果失败: {e}")

    def _generate_multi_stage_report(self, results: List[Dict[str, Any]], output_file: str):
        """生成多阶段评估统计报告"""
        try:
            report_file = output_file.replace('.json', '_multi_stage_report.txt')

            successful_results = [r for r in results if r.get('评估状态') == '成功']

            # 统计各阶段结果
            stage1_pass = sum(1 for r in successful_results
                            if r.get('多阶段结果', {}).get('stage1', {}).get('stage1_result') == '通过')

            type_distribution = {}
            for r in successful_results:
                q_type = r.get('多阶段结果', {}).get('stage2', {}).get('question_type', 'Unknown')
                type_distribution[q_type] = type_distribution.get(q_type, 0) + 1

            compliance_stats = {}
            for r in successful_results:
                judgment = r.get('最终合规判断', 'Unknown')
                compliance_stats[judgment] = compliance_stats.get(judgment, 0) + 1

            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("=" * 80 + "\n")
                f.write("多步骤银行客服问答对合规评估报告\n")
                f.write("=" * 80 + "\n\n")

                f.write(f"评估时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"总文件数: {len(results)}\n")
                f.write(f"成功评估: {len(successful_results)}\n")
                f.write(f"成功率: {len(successful_results)/len(results)*100:.1f}%\n\n")

                f.write("阶段统计:\n")
                f.write("-" * 40 + "\n")
                f.write(f"第一阶段通过: {stage1_pass}/{len(successful_results)}\n")
                f.write(f"第一阶段通过率: {stage1_pass/len(successful_results)*100:.1f}%\n\n")

                f.write("问题类型分布:\n")
                f.write("-" * 40 + "\n")
                for q_type, count in sorted(type_distribution.items()):
                    type_name = QUESTION_TYPES.get(q_type, {}).get('name', q_type)
                    f.write(f"- {type_name}: {count} 个\n")
                f.write("\n")

                f.write("最终合规判断分布:\n")
                f.write("-" * 40 + "\n")
                for judgment, count in sorted(compliance_stats.items()):
                    f.write(f"- {judgment}: {count} 个\n")
                f.write("\n")

                f.write("三阶段流程说明:\n")
                f.write("-" * 40 + "\n")
                f.write("1. 第一阶段：合理性检查 - 判断问题是否适合银行业务场景\n")
                f.write("2. 第二阶段：问题类型分类 - 基于语义理解将问题归类为6种特定类型\n")
                f.write("   (定义/概念解释类、操作类、审核失败/资料补充类、是否类问题、事实性/趋势性问题、其他类)\n")
                f.write("3. 第三阶段：合规评估 - 针对问题类型进行专门的合规性评估并给出最终判断\n")

            logging.info(f"多阶段评估报告已生成: {report_file}")

        except Exception as e:
            logging.error(f"生成多阶段报告失败: {e}")


def main():
    """主函数"""
    # 创建输出文件夹
    base_folder = MULTI_STAGE_CONFIG["output_folder"]
    os.makedirs(base_folder, exist_ok=True)

    # 创建多步骤评估器
    evaluator = MultiStageComplianceEvaluator()

    # 开始评估 - 不需要汇总文件，每个问答对生成独立的结果文件
    logging.info("开始三阶段银行客服问答对合规评估...")
    results = evaluator.process_qa_folder(PATH_CONFIG["qa_folder"])

    logging.info(f"三阶段评估完成！共处理 {len(results)} 个文件")
    logging.info(f"结果已保存到各个独立的 *_result.json 文件中")


if __name__ == "__main__":
    main()

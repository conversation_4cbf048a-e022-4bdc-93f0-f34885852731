# 银行客服问答对合规评估系统

基于qwen2.5-32b-instruct模型的银行客服问答对合规性评估工具。

## 功能特性

- **智能合规评估**: 使用先进的AI模型进行多角度合规性分析
- **批量处理**: 支持批量处理多个问答对文件
- **详细报告**: 生成详细的评估报告和汇总统计
- **多格式转换**: 支持Excel和JSON格式之间的相互转换
- **可配置参数**: 灵活的配置选项，支持不同的评估标准

## 文件结构

```
compliance_project/
├── compliance_evaluator.py    # 主评估脚本
├── excel_to_json.py          # Excel转JSON转换器
├── json_to_excel.py          # JSON转Excel转换器
├── config.py                 # 配置文件
├── README.md                 # 使用说明
├── 问答对/                   # 问答对数据文件夹
│   ├── 001.json
│   ├── 002.json
│   └── ... (101个文件)
├── 合规审查结果/             # 评估结果文件夹
│   ├── 001_result.json
│   ├── 002_result.json
│   └── ... (101个文件)
├── 审核问答对.xlsx           # 问答对Excel文件
└── 合规审查汇总（100-201）.xlsx # 评估结果Excel汇总
```

## 安装依赖

```bash
pip install pandas openai openpyxl
```

## 配置说明

在 `config.py` 文件中配置以下参数：

### API配置
- `api_key`: API密钥
- `base_url`: API基础URL
- `model`: 使用的模型名称

### 模型参数
- `temperature`: 温度参数（建议0.0确保一致性）
- `max_tokens`: 最大输出token数
- `request_delay`: 请求间隔时间（秒）

## 核心脚本使用方法

### 1. 合规评估 (`compliance_evaluator.py`)

**功能**: 对问答对进行AI合规性评估

**使用方法**:
```bash
python compliance_evaluator.py
```

**输入**: `问答对/` 文件夹中的JSON文件
**输出**: `合规审查结果/` 文件夹中的评估结果JSON文件

**说明**:
- 自动读取所有问答对文件
- 使用AI模型进行5轮独立分析
- 生成详细的合规评估报告
- 支持批量处理和进度跟踪

### 2. Excel转JSON (`excel_to_json.py`)

**功能**: 将Excel格式的问答对文件转换为独立的JSON文件

**使用方法**:
```bash
python excel_to_json.py
```

**输入**: `审核问答对.xlsx`
**输出**: `问答对/` 文件夹中的JSON文件（001.json, 002.json, ...）

**说明**:
- 读取Excel文件中的每一行数据
- 转换为标准的JSON格式
- 自动生成编号文件名
- 支持空值处理和数据清洗

### 3. JSON转Excel (`json_to_excel.py`)

**功能**: 将合规审查结果的JSON文件转换为Excel汇总文件

**使用方法**:
```bash
python json_to_excel.py
```

**输入**: `合规审查结果/` 文件夹中的JSON文件
**输出**: `合规审查汇总_YYYYMMDD_HHMMSS.xlsx`

**说明**:
- 读取所有评估结果JSON文件
- 提取关键信息和合规判断
- 生成Excel格式的汇总报告
- 包含统计信息和分析结果

## 数据格式说明

### 问答对JSON格式

```json
{
  "类别": "票据",
  "问题": "客户问题内容",
  "回答": "客服回答内容",
  "分类": "业务分类",
  "优化后的回答": "优化建议",
  "是否可用": "可用/不可用",
  "原因": "判断原因"
}
```

### 评估结果JSON格式

```json
{
  "文件名": "001.json",
  "原始数据": { ... },
  "评估时间": "2024-01-01 12:00:00",
  "合规评估结果": "详细的AI评估分析结果",
  "评估状态": "成功"
}
```

## 合规评估标准

系统基于以下标准进行评估：

1. **信息披露充分性**: 回答是否提供了充分的信息
2. **误导性表达检查**: 是否存在误导性或模糊的表达
3. **条款违反检查**: 是否违反了具体的业务条款
4. **格式规范性**: 语言格式是否符合规范
5. **综合评估**: 准确性、简洁性、用词规范等

每个问答对会进行5轮独立分析，最终通过投票机制确定合规性判断。

## 工作流程

### 典型使用流程

1. **数据准备**:
   - 将问答对数据整理到Excel文件中
   - 运行 `excel_to_json.py` 转换为JSON文件

2. **合规评估**:
   - 运行 `compliance_evaluator.py` 进行AI评估
   - 系统自动处理所有问答对文件

3. **结果汇总**:
   - 运行 `json_to_excel.py` 生成Excel汇总报告
   - 查看和分析评估结果

### 配置调整

可以在 `config.py` 中调整以下参数：

- `temperature`: 控制AI输出的随机性（建议0.0确保一致性）
- `max_tokens`: 最大输出长度
- `request_delay`: 请求间隔时间（避免API限制）

## 故障排除

### 常见问题

1. **API调用失败**
   - 检查API密钥和基础URL配置
   - 确认网络连接正常
   - 检查API配额限制

2. **文件读取错误**
   - 确认文件格式正确（UTF-8编码）
   - 检查文件权限
   - 验证JSON格式有效性

3. **Excel文件问题**
   - 确保Excel文件包含正确的列名
   - 检查数据格式和编码
   - 验证文件路径正确

## 注意事项

- 确保API密钥配置正确
- 建议设置适当的请求延迟避免API限制
- 大批量处理时注意监控API使用量
- 定期备份重要的评估结果

## 版本信息

- 版本: 2.0.0
- 支持的模型: qwen2.5-32b-instruct
- Python版本要求: 3.8+

## 许可证

本项目仅供学习和研究使用。

---

**注意**: 请确保在使用前正确配置API密钥，并遵守相关的使用条款和隐私政策。

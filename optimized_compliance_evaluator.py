#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
银行客服问答对合规评估脚本（优化后回答版本）
使用qwen2.5-32b-instruct模型对"优化后的回答"字段进行合规性评估
"""

import openai
import json
import os
import time
from datetime import datetime
from typing import Dict, List, Any
import logging
from config import API_CONFIG, PATH_CONFIG, MODEL_CONFIG, SYSTEM_PROMPT, COMPLIANCE_CRITERIA, OUTPUT_CONFIG

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('optimized_compliance_evaluation.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class OptimizedComplianceEvaluator:
    """优化后回答合规评估器"""
    
    def __init__(self, api_key: str = None, base_url: str = None):
        """
        初始化优化后回答合规评估器

        Args:
            api_key: API密钥（可选，默认从配置文件读取）
            base_url: API基础URL（可选，默认从配置文件读取）
        """
        self.client = openai.OpenAI(
            base_url=base_url or API_CONFIG["base_url"],
            api_key=api_key or API_CONFIG["api_key"]
        )
        self.model = API_CONFIG["model"]
        self.system_prompt = SYSTEM_PROMPT
        self.compliance_criteria = COMPLIANCE_CRITERIA
        self.model_config = MODEL_CONFIG
    
    def load_qa_file(self, file_path: str) -> Dict[str, Any]:
        """
        加载问答对文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            问答对数据字典
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return data
        except Exception as e:
            logging.error(f"加载文件 {file_path} 失败: {e}")
            return None
    
    def is_optimized_answer_valid(self, qa_data: Dict[str, Any]) -> bool:
        """
        检查优化后的回答是否有效（非空且非null）
        
        Args:
            qa_data: 问答对数据
            
        Returns:
            True如果优化后的回答有效，False否则
        """
        optimized_answer = qa_data.get("优化后的回答")
        return optimized_answer is not None and optimized_answer.strip() != ""
    
    def format_qa_for_evaluation(self, qa_data: Dict[str, Any]) -> str:
        """
        格式化问答对用于评估（使用优化后的回答）
        
        Args:
            qa_data: 问答对数据
            
        Returns:
            格式化的评估文本
        """
        category = qa_data.get("类别", "未知")
        question = qa_data.get("问题", "")
        optimized_answer = qa_data.get("优化后的回答", "")
        classification = qa_data.get("分类", "")
        
        evaluation_text = f"""
【业务类别】: {category}
【客户问题】: {question}
【客服回答】: {optimized_answer}
【业务分类】: {classification if classification else "未分类"}

请根据银行客服合规标准，对上述问答对进行全面的合规性评估。
        """.strip()
        
        return evaluation_text
    
    def clean_and_validate_output(self, raw_output: str) -> str:
        """
        清理和验证模型输出格式

        Args:
            raw_output: 原始模型输出

        Returns:
            清理后的输出
        """
        cleaned_output = raw_output

        # 移除<think>标签及其内容
        import re
        think_pattern = r'<think>.*?</think>'
        cleaned_output = re.sub(think_pattern, '', cleaned_output, flags=re.DOTALL)

        # 清理多余的空行
        cleaned_output = re.sub(r'\n\s*\n\s*\n', '\n\n', cleaned_output)
        cleaned_output = cleaned_output.strip()

        # 验证必需的格式元素
        required_patterns = [
            r'\*\*最终合规判断：\*\*',
            r'\*\*判断理由：\*\*',
            r'\*\*详细分析：\*\*',
            r'第一次审核：',
            r'第二次审核：',
            r'第三次审核：',
            r'第四次审核：',
            r'第五次审核：',
            r'\*\*投票结果统计：\*\*',
            r'\*\*最终优化建议：\*\*'
        ]

        missing_elements = []
        for pattern in required_patterns:
            if not re.search(pattern, cleaned_output):
                missing_elements.append(pattern)

        if missing_elements:
            logging.warning(f"输出格式不完整，缺少元素: {missing_elements}")

        return cleaned_output

    def evaluate_compliance(self, qa_text: str) -> str:
        """
        使用模型评估合规性

        Args:
            qa_text: 格式化的问答对文本

        Returns:
            模型评估结果
        """
        try:
            messages = [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": qa_text}
            ]

            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=self.model_config["temperature"],
                max_tokens=self.model_config["max_tokens"]
            )

            raw_output = response.choices[0].message.content
            cleaned_output = self.clean_and_validate_output(raw_output)

            return cleaned_output

        except Exception as e:
            logging.error(f"API调用失败: {e}")
            return f"评估失败: {str(e)}"

    def process_single_file(self, file_path: str, save_individual: bool = False, output_folder: str = None) -> Dict[str, Any]:
        """
        处理单个问答对文件（仅处理有优化后回答的文件）

        Args:
            file_path: 文件路径
            save_individual: 是否保存单独的结果文件
            output_folder: 输出文件夹路径（当save_individual=True时使用）

        Returns:
            评估结果字典，如果优化后回答为空则返回None
        """
        logging.info(f"正在处理文件: {file_path}")

        # 加载问答对数据
        qa_data = self.load_qa_file(file_path)
        if qa_data is None:
            return None

        # 检查优化后的回答是否有效
        if not self.is_optimized_answer_valid(qa_data):
            logging.info(f"跳过文件 {file_path}：优化后的回答为空或null")
            return None

        # 格式化问答对
        qa_text = self.format_qa_for_evaluation(qa_data)

        # 进行合规评估
        evaluation_result = self.evaluate_compliance(qa_text)

        # 构建结果
        result = {
            "文件名": os.path.basename(file_path),
            "原始数据": qa_data,
            "评估时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "合规评估结果": evaluation_result,
            "评估状态": "成功",
            "评估类型": "优化后回答"
        }

        # 如果需要保存单独的结果文件
        if save_individual and output_folder:
            self.save_individual_result(result, file_path, output_folder)

        return result

    def save_individual_result(self, result: Dict[str, Any], original_file_path: str, output_folder: str):
        """
        保存单个问答对的评估结果到独立文件

        Args:
            result: 评估结果字典
            original_file_path: 原始问答对文件路径
            output_folder: 输出文件夹路径
        """
        try:
            # 创建输出目录
            os.makedirs(output_folder, exist_ok=True)

            # 生成输出文件名：从原始文件名生成对应的结果文件名
            original_filename = os.path.basename(original_file_path)
            filename_without_ext = os.path.splitext(original_filename)[0]
            output_filename = f"{filename_without_ext}_optimized_result.json"
            output_file_path = os.path.join(output_folder, output_filename)

            # 保存结果到JSON文件
            with open(output_file_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)

            logging.info(f"单个结果已保存到: {output_file_path}")

        except Exception as e:
            logging.error(f"保存单个结果失败: {e}")

    def process_qa_folder(self, folder_path: str, output_file: str = None, save_individual: bool = True) -> List[Dict[str, Any]]:
        """
        批量处理问答对文件夹（仅处理有优化后回答的文件）

        Args:
            folder_path: 问答对文件夹路径
            output_file: 输出文件路径（可选，用于保存汇总结果）
            save_individual: 是否为每个问答对保存单独的结果文件（默认True）

        Returns:
            所有评估结果列表
        """
        if not os.path.exists(folder_path):
            logging.error(f"文件夹不存在: {folder_path}")
            return []

        # 获取所有JSON文件
        json_files = [f for f in os.listdir(folder_path) if f.endswith('.json')]
        json_files.sort()  # 按文件名排序

        logging.info(f"找到 {len(json_files)} 个JSON文件")

        # 确定输出文件夹
        individual_output_folder = None
        if save_individual:
            if output_file:
                # 如果指定了输出文件，使用其目录作为单个结果的输出目录
                individual_output_folder = os.path.dirname(output_file) or PATH_CONFIG["output_folder"]
            else:
                # 否则使用默认输出目录
                individual_output_folder = PATH_CONFIG["output_folder"]

            # 确保输出目录存在
            os.makedirs(individual_output_folder, exist_ok=True)
            logging.info(f"单个结果将保存到: {individual_output_folder}")

        all_results = []
        processed_count = 0
        skipped_count = 0

        for i, filename in enumerate(json_files, 1):
            file_path = os.path.join(folder_path, filename)

            try:
                result = self.process_single_file(
                    file_path,
                    save_individual=save_individual,
                    output_folder=individual_output_folder
                )
                if result:
                    all_results.append(result)
                    processed_count += 1
                    logging.info(f"完成 {processed_count}/{len(json_files)}: {filename}")
                else:
                    skipped_count += 1
                    logging.info(f"跳过文件 {i}/{len(json_files)}: {filename} (优化后回答为空)")

                # 添加延迟避免API限制
                if result:  # 只有在实际调用API时才延迟
                    time.sleep(self.model_config["request_delay"])

            except Exception as e:
                logging.error(f"处理文件 {filename} 时出错: {e}")
                skipped_count += 1
                continue

        logging.info(f"处理完成：成功处理 {processed_count} 个文件，跳过 {skipped_count} 个文件")

        # 保存汇总结果（如果指定了输出文件）
        if output_file:
            self.save_results(all_results, output_file)

        return all_results

    def save_results(self, results: List[Dict[str, Any]], output_file: str):
        """
        保存评估结果到文件

        Args:
            results: 评估结果列表
            output_file: 输出文件路径
        """
        try:
            # 创建输出目录
            os.makedirs(os.path.dirname(output_file) if os.path.dirname(output_file) else '.', exist_ok=True)

            # 保存为JSON格式
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)

            logging.info(f"评估结果已保存到: {output_file}")

            # 生成汇总报告
            summary_file = output_file.replace('.json', '_summary.txt')
            self.generate_summary_report(results, summary_file)

            # 生成Excel报告（如果可能）
            excel_file = output_file.replace('.json', '_report.xlsx')
            self.export_to_excel(results, excel_file)

        except Exception as e:
            logging.error(f"保存结果失败: {e}")

    def generate_summary_report(self, results: List[Dict[str, Any]], summary_file: str):
        """
        生成汇总报告

        Args:
            results: 评估结果列表
            summary_file: 汇总报告文件路径
        """
        try:
            successful_results = [r for r in results if r.get('评估状态') == '成功']
            failed_results = [r for r in results if r.get('评估状态') != '成功']

            with open(summary_file, 'w', encoding='utf-8') as f:
                f.write("=" * 80 + "\n")
                f.write("银行客服问答对合规评估汇总报告（优化后回答版本）\n")
                f.write("=" * 80 + "\n\n")

                # 基本统计信息
                f.write(f"评估时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"总处理文件数: {len(results)}\n")
                f.write(f"成功评估: {len(successful_results)}\n")
                f.write(f"失败评估: {len(failed_results)}\n")
                f.write(f"成功率: {len(successful_results)/len(results)*100:.1f}%\n")
                f.write(f"评估类型: 优化后回答\n\n")

                # 失败文件列表
                if failed_results:
                    f.write("失败文件列表:\n")
                    f.write("-" * 40 + "\n")
                    for result in failed_results:
                        f.write(f"- {result.get('文件名', 'Unknown')}\n")
                    f.write("\n")

                # 业务类别统计
                categories = {}
                for result in successful_results:
                    category = result.get('原始数据', {}).get('类别', '未知')
                    categories[category] = categories.get(category, 0) + 1

                if categories:
                    f.write("业务类别分布:\n")
                    f.write("-" * 40 + "\n")
                    for category, count in sorted(categories.items()):
                        f.write(f"- {category}: {count} 个文件\n")
                    f.write("\n")

                # 合规标准参考
                f.write("合规标准参考:\n")
                f.write("-" * 40 + "\n")
                for criterion, description in self.compliance_criteria.items():
                    f.write(f"- {criterion}: {description}\n")
                f.write("\n")

                f.write("详细评估结果请查看对应的JSON文件。\n")
                f.write("\n注意：本次评估仅针对包含'优化后的回答'字段的问答对进行。\n")

            logging.info(f"汇总报告已生成: {summary_file}")

        except Exception as e:
            logging.error(f"生成汇总报告失败: {e}")

    def export_to_excel(self, results: List[Dict[str, Any]], excel_file: str):
        """
        导出结果到Excel文件

        Args:
            results: 评估结果列表
            excel_file: Excel文件路径
        """
        try:
            import pandas as pd

            # 准备数据
            data = []
            for result in results:
                original_data = result.get('原始数据', {})
                data.append({
                    '文件名': result.get('文件名', ''),
                    '业务类别': original_data.get('类别', ''),
                    '客户问题': original_data.get('问题', ''),
                    '原始回答': original_data.get('回答', ''),
                    '优化后回答': original_data.get('优化后的回答', ''),
                    '业务分类': original_data.get('分类', ''),
                    '原始可用性': original_data.get('是否可用', ''),
                    '评估时间': result.get('评估时间', ''),
                    '评估状态': result.get('评估状态', ''),
                    '评估类型': result.get('评估类型', ''),
                    '合规评估结果': result.get('合规评估结果', '')
                })

            # 创建DataFrame并保存
            df = pd.DataFrame(data)
            df.to_excel(excel_file, index=False, engine='openpyxl')

            logging.info(f"Excel报告已生成: {excel_file}")

        except ImportError:
            logging.warning("未安装pandas或openpyxl，跳过Excel导出")
        except Exception as e:
            logging.error(f"导出Excel失败: {e}")


def main():
    """主函数"""
    # 创建输出文件夹
    output_folder = PATH_CONFIG["output_folder"] + "_优化后回答"
    os.makedirs(output_folder, exist_ok=True)

    # 输出文件路径
    OUTPUT_FILE = os.path.join(
        output_folder,
        f"optimized_compliance_evaluation_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    )

    # 创建评估器
    evaluator = OptimizedComplianceEvaluator()

    # 开始评估
    logging.info("开始银行客服问答对合规评估（优化后回答版本）...")
    results = evaluator.process_qa_folder(PATH_CONFIG["qa_folder"], OUTPUT_FILE)

    logging.info(f"评估完成！共处理 {len(results)} 个有效文件")
    logging.info(f"结果已保存到: {OUTPUT_FILE}")


if __name__ == "__main__":
    main()

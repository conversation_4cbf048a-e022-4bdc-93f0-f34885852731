#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第三阶段完整测试脚本：特定类型合规评估
测试所有问答对100-201文件夹内的文件
需要先运行第二阶段获取问题类型分类结果
"""

import json
import os
import time
from datetime import datetime
from multi_stage_compliance_evaluator import MultiStageComplianceEvaluator
from multi_stage_config import QUESTION_TYPES
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('stage3_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def load_stage2_results():
    """加载第二阶段的结果"""
    stage2_folder = "第二阶段测试结果"
    if not os.path.exists(stage2_folder):
        print(f"❌ 第二阶段结果文件夹不存在: {stage2_folder}")
        print("请先运行 python test_stage2_complete.py")
        return None

    stage2_results = {}
    for filename in os.listdir(stage2_folder):
        if filename.endswith('_stage2_result.json'):
            file_id = filename.replace('_stage2_result.json', '')
            file_path = os.path.join(stage2_folder, filename)

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    stage2_data = data.get("第二阶段结果", {})
                    question_type = stage2_data.get("question_type", "OPERATION")
                    stage2_results[file_id] = question_type
            except Exception as e:
                print(f"⚠️ 加载第二阶段结果失败: {filename}, 错误: {e}")
                stage2_results[file_id] = "OPERATION"  # 默认为操作类问题

    return stage2_results

def test_stage3_all_files():
    """测试第三阶段：对所有文件进行特定类型合规评估"""
    print("=" * 80)
    print("第三阶段完整测试：特定类型合规评估")
    print("=" * 80)

    # 加载第二阶段结果
    stage2_results = load_stage2_results()
    if stage2_results is None:
        return False

    print(f"📋 加载了 {len(stage2_results)} 个第二阶段分类结果")

    # 创建评估器
    evaluator = MultiStageComplianceEvaluator()

    # 输入和输出目录
    input_folder = "问答对100-201"
    output_folder = "第三阶段测试结果"
    os.makedirs(output_folder, exist_ok=True)

    # 获取所有JSON文件
    if not os.path.exists(input_folder):
        print(f"❌ 输入文件夹不存在: {input_folder}")
        return False

    json_files = [f for f in os.listdir(input_folder) if f.endswith('.json')]
    json_files.sort()

    print(f"📁 找到 {len(json_files)} 个JSON文件")
    print(f"📂 输出目录: {output_folder}")
    print()

    # 统计信息
    total_files = len(json_files)
    success_count = 0
    error_count = 0
    compliance_stats = {"合规": 0, "不合规": 0}
    type_compliance_stats = {}

    all_results = []

    # 处理每个文件
    for i, filename in enumerate(json_files, 1):
        file_path = os.path.join(input_folder, filename)
        file_id = os.path.splitext(filename)[0]

        print(f"🔄 处理 {i}/{total_files}: {filename}")

        try:
            # 加载问答对数据
            qa_data = evaluator.load_qa_file(file_path)
            if qa_data is None:
                print(f"   ❌ 文件加载失败")
                error_count += 1
                continue

            # 获取问题类型
            question_type = stage2_results.get(file_id, "OPERATION")
            type_name = QUESTION_TYPES.get(question_type, {}).get('name', question_type)

            # 显示问题内容
            question = qa_data.get("问题", "")
            answer = qa_data.get("回答", "")
            category = qa_data.get("类别", "")
            print(f"   📝 类别: {category}")
            print(f"   📝 问题类型: {question_type} ({type_name})")
            print(f"   📝 问题: {question[:50]}{'...' if len(question) > 50 else ''}")
            print(f"   📝 回答: {answer[:50]}{'...' if len(answer) > 50 else ''}")

            # 执行第三阶段测试
            stage3_result = evaluator.stage3_type_specific_evaluation(qa_data, question_type)

            if stage3_result.get("error"):
                print(f"   ❌ API调用失败: {stage3_result['error']}")
                error_count += 1
                continue

            # 提取关键结果
            compliance_judgment = stage3_result.get("compliance_judgment", "未知")
            result_status = stage3_result.get("stage3_result", "未知")
            evaluated_type = stage3_result.get("evaluated_type", question_type)

            # 根据问题类型提取不同的评分
            scores = {}
            if question_type == "DEFINITION":
                scores = {
                    "accuracy_score": stage3_result.get("accuracy_score", 0),
                    "clarity_score": stage3_result.get("clarity_score", 0),
                    "completeness_score": stage3_result.get("completeness_score", 0)
                }
            elif question_type == "OPERATION":
                scores = {
                    "operability_score": stage3_result.get("operability_score", 0),
                    "clarity_score": stage3_result.get("clarity_score", 0),
                    "completeness_score": stage3_result.get("completeness_score", 0)
                }
            elif question_type == "AUDIT_FAILURE":
                scores = {
                    "helpfulness_score": stage3_result.get("helpfulness_score", 0),
                    "appropriateness_score": stage3_result.get("appropriateness_score", 0),
                    "guidance_quality": stage3_result.get("guidance_quality", 0)
                }
            elif question_type == "YES_NO":
                scores = {
                    "clarity_score": stage3_result.get("clarity_score", 0),
                    "accuracy_score": stage3_result.get("accuracy_score", 0),
                    "appropriateness_score": stage3_result.get("appropriateness_score", 0)
                }
            elif question_type == "FACTUAL":
                scores = {
                    "evidence_support": stage3_result.get("evidence_support", 0),
                    "accuracy_score": stage3_result.get("accuracy_score", 0),
                    "reliability_score": stage3_result.get("reliability_score", 0)
                }

            specific_issues = stage3_result.get("specific_issues", [])
            optimization_suggestions = stage3_result.get("optimization_suggestions", [])

            print(f"   ✅ 合规判断: {compliance_judgment}")
            print(f"   📊 评分: {scores}")
            print(f"   🔄 阶段结果: {result_status}")
            if specific_issues:
                print(f"   ⚠️ 具体问题: {', '.join(specific_issues[:3])}{'...' if len(specific_issues) > 3 else ''}")
            if optimization_suggestions:
                print(f"   💡 优化建议: {', '.join(optimization_suggestions[:2])}{'...' if len(optimization_suggestions) > 2 else ''}")

            # 统计
            success_count += 1
            if compliance_judgment in compliance_stats:
                compliance_stats[compliance_judgment] += 1

            # 按类型统计合规性
            if question_type not in type_compliance_stats:
                type_compliance_stats[question_type] = {"合规": 0, "不合规": 0}
            if compliance_judgment in type_compliance_stats[question_type]:
                type_compliance_stats[question_type][compliance_judgment] += 1

            # 构建完整结果
            complete_result = {
                "文件名": filename,
                "文件ID": file_id,
                "原始数据": qa_data,
                "问题类型": question_type,
                "第三阶段结果": {
                    "compliance_judgment": compliance_judgment,
                    "evaluated_type": evaluated_type,
                    "scores": scores,
                    "specific_issues": specific_issues,
                    "optimization_suggestions": optimization_suggestions,
                    "stage3_result": result_status,
                    "raw_response": stage3_result.get("raw_response", ""),
                    "stage": "stage3_type_specific"
                },
                "测试时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "测试状态": "成功"
            }

            # 保存单个文件结果
            output_filename = f"{file_id}_stage3_result.json"
            output_filepath = os.path.join(output_folder, output_filename)

            with open(output_filepath, 'w', encoding='utf-8') as f:
                json.dump(complete_result, f, ensure_ascii=False, indent=2)

            all_results.append(complete_result)

            # 添加延迟避免API限制
            time.sleep(1)

        except Exception as e:
            print(f"   ❌ 处理失败: {e}")
            error_count += 1
            logging.error(f"处理文件 {filename} 时出错: {e}")
            continue

        print()

    # 生成汇总报告
    generate_stage3_summary(all_results, output_folder, {
        'total_files': total_files,
        'success_count': success_count,
        'error_count': error_count,
        'compliance_stats': compliance_stats,
        'type_compliance_stats': type_compliance_stats
    })

    return True

def generate_stage3_summary(results, output_folder, stats):
    """生成第三阶段汇总报告"""

    # 保存汇总JSON
    summary_file = os.path.join(output_folder, f"stage3_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    summary_data = {
        "测试时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "统计信息": stats,
        "详细结果": results
    }

    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(summary_data, f, ensure_ascii=False, indent=2)

    # 生成文本报告
    report_file = os.path.join(output_folder, f"stage3_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt")

    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("=" * 80 + "\n")
        f.write("第三阶段测试报告：特定类型合规评估\n")
        f.write("=" * 80 + "\n\n")

        f.write(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"总文件数: {stats['total_files']}\n")
        f.write(f"成功处理: {stats['success_count']}\n")
        f.write(f"处理失败: {stats['error_count']}\n")
        f.write(f"成功率: {stats['success_count']/stats['total_files']*100:.1f}%\n\n")

        f.write("整体合规性分布:\n")
        f.write("-" * 40 + "\n")
        for judgment, count in stats['compliance_stats'].items():
            percentage = count / stats['success_count'] * 100 if stats['success_count'] > 0 else 0
            f.write(f"{judgment}: {count} 个 ({percentage:.1f}%)\n")
        f.write("\n")

        f.write("按问题类型的合规性分布:\n")
        f.write("-" * 40 + "\n")
        for q_type, compliance_data in stats['type_compliance_stats'].items():
            type_name = QUESTION_TYPES.get(q_type, {}).get('name', q_type)
            total_type = sum(compliance_data.values())
            f.write(f"{q_type} ({type_name}) - 总计: {total_type} 个\n")
            for judgment, count in compliance_data.items():
                percentage = count / total_type * 100 if total_type > 0 else 0
                f.write(f"  {judgment}: {count} 个 ({percentage:.1f}%)\n")
            f.write("\n")

        # 详细结果列表
        f.write("详细结果:\n")
        f.write("-" * 40 + "\n")
        for result in results:
            stage3_data = result.get("第三阶段结果", {})
            judgment = stage3_data.get("compliance_judgment", "未知")
            q_type = result.get("问题类型", "未知")
            scores = stage3_data.get("scores", {})
            avg_score = sum(scores.values()) / len(scores) if scores else 0
            f.write(f"{result['文件ID']}: {judgment} ({q_type}, 平均评分: {avg_score:.2f})\n")

        f.write(f"\n详细结果已保存到: {summary_file}\n")

    print("=" * 80)
    print("第三阶段测试完成")
    print("=" * 80)
    print(f"📊 总文件数: {stats['total_files']}")
    print(f"✅ 成功处理: {stats['success_count']}")
    print(f"❌ 处理失败: {stats['error_count']}")
    print(f"📈 成功率: {stats['success_count']/stats['total_files']*100:.1f}%")
    print()
    print("整体合规性分布:")
    for judgment, count in stats['compliance_stats'].items():
        percentage = count / stats['success_count'] * 100 if stats['success_count'] > 0 else 0
        print(f"  {judgment}: {count} 个 ({percentage:.1f}%)")
    print()
    print("按问题类型的合规性:")
    for q_type, compliance_data in stats['type_compliance_stats'].items():
        type_name = QUESTION_TYPES.get(q_type, {}).get('name', q_type)
        total_type = sum(compliance_data.values())
        compliant_count = compliance_data.get("合规", 0)
        compliant_rate = compliant_count / total_type * 100 if total_type > 0 else 0
        print(f"  {q_type} ({type_name}): {compliant_count}/{total_type} 合规 ({compliant_rate:.1f}%)")
    print()
    print(f"📄 汇总报告: {report_file}")
    print(f"📋 详细数据: {summary_file}")

def main():
    """主函数"""
    print("开始第三阶段完整测试...")
    print("请确保已激活tradingagents虚拟环境")
    print("请确保已运行第二阶段测试获取问题类型分类结果")
    print()

    try:
        success = test_stage3_all_files()
        if success:
            print("\n🎉 第三阶段测试成功完成！")
        else:
            print("\n❌ 第三阶段测试失败")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

# 多步骤银行客服合规审计系统

## 项目概述

本项目实现了一个四阶段的银行客服问答对合规审计系统，以结构化的多步骤流程取代原有的单一模型调用方法。系统保持了现有的输入/输出逻辑和文件保存机制，同时提供了更精细化和专业化的合规评估能力。

## 核心特性

- ✅ **四阶段结构化流程**：合理性检查 → 问题分类 → 类型评估 → 最终决策
- ✅ **专业化评估标准**：针对5种不同问题类型的专门评估逻辑
- ✅ **完全兼容原系统**：保持相同的输入/输出格式和文件保存机制
- ✅ **可追溯性**：每个阶段的结果都被保存，便于调试和优化
- ✅ **模块化设计**：每个阶段可以独立测试和优化
- ✅ **错误处理机制**：包含重试机制和多种错误处理策略

## 文件结构

```
├── multi_stage_config.py              # 多阶段配置文件
├── multi_stage_compliance_evaluator.py # 主要编排脚本
├── test_multi_stage_compliance.py     # 完整测试脚本
├── test_single_qa.py                  # 单个问答对测试脚本
├── quick_test.py                      # 快速功能测试脚本
├── demo_multi_stage.py                # 系统演示脚本
├── 多阶段合规审计系统文档.md           # 详细技术文档
└── README_多步骤合规审计系统.md        # 本文件
```

## 四阶段流程详解

### 第一阶段：合理性检查
- **目标**：判断问题是否在银行业务背景下合理
- **输出**：合理/不合理的二元判断
- **终止条件**：如果问题不合理，直接判定为不合规

### 第二阶段：问题类型分类
- **目标**：将问题归类为5种特定类型之一
- **类型**：
  1. 定义/概念解释问题 (DEFINITION)
  2. 操作问题 (OPERATION)
  3. 审核失败/文件补充问题 (AUDIT_FAILURE)
  4. 是/否问题 (YES_NO)
  5. 事实性/趋势性问题 (FACTUAL)

### 第三阶段：特定类型合规评估
- **目标**：针对不同问题类型使用专门的评估标准
- **特点**：每种类型都有定制化的评估重点和合规标准

### 第四阶段：最终决策综合
- **目标**：综合前三阶段结果，生成最终合规判定
- **输出**：与原系统完全兼容的5轮分析格式

## 快速开始

### 1. 基本功能测试
```bash
python quick_test.py
```

### 2. 单个问答对测试
```bash
python test_single_qa.py
```

### 3. 系统演示
```bash
python demo_multi_stage.py
```

### 4. 完整测试
```bash
python test_multi_stage_compliance.py
```

### 5. 正式运行
```bash
python multi_stage_compliance_evaluator.py
```

## 使用示例

### 基本使用
```python
from multi_stage_compliance_evaluator import MultiStageComplianceEvaluator

# 创建评估器
evaluator = MultiStageComplianceEvaluator()

# 处理单个问答对
qa_data = {
    "类别": "存款",
    "问题": "什么是定期存款？",
    "回答": "定期存款是指您将资金存入银行，约定存期和利率，到期后支取本息的储蓄方式。",
    # ... 其他字段
}

result = evaluator.process_single_qa(qa_data, "test_001")
print(f"最终判断: {result['最终合规判断']}")
```

### 批量处理
```python
# 批量处理文件夹
results = evaluator.process_qa_folder("问答对100-201")
print(f"处理了 {len(results)} 个文件")
```

## 输出结构

### 兼容原系统的输出格式
```json
{
    "文件名": "001.json",
    "原始数据": {...},
    "评估时间": "2025-08-02 10:30:00",
    "合规评估结果": "完整的5轮分析格式文本",
    "评估状态": "成功"
}
```

### 多阶段详细结果
```json
{
    "文件ID": "001",
    "多阶段结果": {
        "stage1": {...},  # 合理性检查结果
        "stage2": {...},  # 问题分类结果
        "stage3": {...},  # 类型评估结果
        "stage4": {...}   # 最终决策结果
    },
    "最终合规判断": "合规/不合规",
    "评估状态": "成功"
}
```

## 输出目录结构

```
多阶段合规审查结果/
├── 中间结果/
│   ├── 第一阶段_合理性检查/
│   ├── 第二阶段_问题分类/
│   ├── 第三阶段_类型评估/
│   └── 第四阶段_最终决策/
├── 最终合规结果/
└── 汇总报告文件
```

## 配置说明

### 多阶段流程配置
```python
MULTI_STAGE_CONFIG = {
    "enable_multi_stage": True,           # 启用多阶段模式
    "save_intermediate_results": True,    # 保存中间结果
    "stage_delay": 1,                     # 阶段间延迟（秒）
    "max_retries": 3,                     # 最大重试次数
    "output_folder": "多阶段合规审查结果",
    "intermediate_folder": "中间结果"
}
```

## 与原系统的对比

| 特性 | 原系统 | 新系统 |
|------|--------|--------|
| 调用方式 | 单一模型调用 | 四阶段结构化调用 |
| 评估标准 | 通用标准 | 类型特定标准 |
| 可追溯性 | 有限 | 完全可追溯 |
| 模块化程度 | 低 | 高 |
| 错误处理 | 基本 | 完善 |
| 输出兼容性 | - | 100%兼容 |

## 系统优势

1. **更高的准确性**：针对不同问题类型的专业化评估
2. **更好的可维护性**：模块化设计便于调试和优化
3. **更强的可扩展性**：易于添加新的问题类型和评估标准
4. **更完善的错误处理**：多层次的错误处理和重试机制
5. **更详细的日志**：每个阶段的详细执行记录

## 性能考虑

- **API调用次数**：相比原系统增加4倍，但每次调用复杂度降低
- **处理时间**：略有增加，但准确性显著提升
- **资源使用**：中间结果存储需要额外磁盘空间

## 扩展指南

### 添加新问题类型
1. 在 `multi_stage_config.py` 的 `QUESTION_TYPES` 中添加新类型
2. 在 `STAGE3_TYPE_SPECIFIC_PROMPTS` 中添加对应的评估提示词
3. 测试新类型的分类和评估功能

### 自定义评估标准
1. 修改对应问题类型的第三阶段提示词
2. 调整评估维度和评分标准
3. 更新输出格式（如需要）

## 故障排除

### 常见问题
1. **API调用失败**：检查网络连接和API配置
2. **JSON解析错误**：系统包含多种解析策略，通常能自动处理
3. **文件权限问题**：确保输出目录有写入权限
4. **内存不足**：处理大量文件时考虑分批处理

### 调试建议
1. 使用 `test_single_qa.py` 测试单个问答对
2. 检查中间结果文件了解各阶段执行情况
3. 查看日志文件获取详细错误信息

## 技术支持

如需详细的技术文档，请参考：
- `多阶段合规审计系统文档.md` - 完整技术文档
- `demo_multi_stage.py` - 系统演示和使用示例
- 各测试脚本 - 功能验证和使用参考

## 版本信息

- **版本**：1.0.0
- **创建日期**：2025-08-02
- **兼容性**：完全兼容原有合规评估系统
- **依赖**：openai, json, os, time, datetime, logging

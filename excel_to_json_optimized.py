#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel文件转JSON文件脚本（优化版）
将审核问答对（100-201）.xlsx文件转换为独立的JSON文件
特殊处理：优先使用优化后的回答，如果没有则使用原回答
"""

import pandas as pd
import json
import os
from pathlib import Path

def excel_to_json_files_optimized(excel_path, output_folder="问答对100-201(优化后)", start_index=1):
    """
    将Excel文件转换为多个JSON文件（优化版）
    
    Args:
        excel_path: Excel文件路径
        output_folder: 输出文件夹路径
        start_index: 起始编号（默认从1开始）
    
    Returns:
        转换成功的文件数量
    """
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_path)
        print(f"成功读取Excel文件: {excel_path}")
        print(f"数据行数: {len(df)}")
        print(f"列名: {list(df.columns)}")
        
        # 创建输出文件夹
        os.makedirs(output_folder, exist_ok=True)
        
        success_count = 0
        optimized_used_count = 0  # 使用优化后回答的计数
        original_used_count = 0   # 使用原回答的计数
        
        # 遍历每一行数据
        for index, row in df.iterrows():
            try:
                # 构建JSON数据结构
                json_data = {}
                
                # 处理类别
                if '类别' in row and pd.notna(row['类别']):
                    json_data['类别'] = str(row['类别']).strip()
                else:
                    json_data['类别'] = ""
                
                # 处理问题
                if '问题' in row and pd.notna(row['问题']):
                    json_data['问题'] = str(row['问题']).strip()
                else:
                    json_data['问题'] = ""
                
                # 处理回答：优先使用优化后的回答，如果没有则使用原回答
                final_answer = ""
                answer_source = ""
                
                # 检查是否有优化后的回答
                if '优化后的回答' in row and pd.notna(row['优化后的回答']):
                    optimized_answer = str(row['优化后的回答']).strip()
                    if optimized_answer:  # 确保不是空字符串
                        final_answer = optimized_answer
                        answer_source = "优化后的回答"
                        optimized_used_count += 1
                
                # 如果没有优化后的回答，使用原回答
                if not final_answer:
                    if '回答' in row and pd.notna(row['回答']):
                        original_answer = str(row['回答']).strip()
                        if original_answer:
                            final_answer = original_answer
                            answer_source = "原回答"
                            original_used_count += 1
                
                json_data['回答'] = final_answer
                
                # 处理分类
                if '分类' in row and pd.notna(row['分类']):
                    json_data['分类'] = str(row['分类']).strip()
                else:
                    json_data['分类'] = None
                
                
                # 处理是否可用
                if '是否可用' in row and pd.notna(row['是否可用']):
                    json_data['是否可用'] = str(row['是否可用']).strip()
                else:
                    json_data['是否可用'] = ""
                
                # 处理原因
                if '原因' in row and pd.notna(row['原因']):
                    json_data['原因'] = str(row['原因']).strip()
                else:
                    json_data['原因'] = None
                
                # 生成文件名
                file_number = start_index + index
                filename = f"{file_number:03d}.json"
                filepath = os.path.join(output_folder, filename)
                
                # 保存JSON文件
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(json_data, f, ensure_ascii=False, indent=2)
                
                success_count += 1
                print(f"已转换: {filename} (使用{answer_source})")
                
            except Exception as e:
                print(f"转换第 {index + 1} 行数据时出错: {e}")
                continue
        
        print(f"\n转换完成!")
        print(f"成功转换: {success_count} 个文件")
        print(f"使用优化后回答: {optimized_used_count} 个")
        print(f"使用原回答: {original_used_count} 个")
        print(f"输出目录: {output_folder}")
        
        return success_count
        
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return 0

def verify_conversion(output_folder, sample_count=5):
    """验证转换结果"""
    print(f"\n验证转换结果:")
    print("=" * 50)
    
    if not os.path.exists(output_folder):
        print("输出文件夹不存在")
        return
    
    json_files = [f for f in os.listdir(output_folder) if f.endswith('.json')]
    json_files.sort()
    
    print(f"生成的JSON文件数量: {len(json_files)}")
    
    if json_files:
        print(f"\n前{min(sample_count, len(json_files))}个文件内容预览:")
        print("-" * 50)
        
        for i, filename in enumerate(json_files[:sample_count]):
            filepath = os.path.join(output_folder, filename)
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                print(f"\n文件: {filename}")
                print(f"类别: {data.get('类别', 'N/A')}")
                print(f"问题: {data.get('问题', 'N/A')[:50]}...")
                print(f"回答: {data.get('回答', 'N/A')[:50]}...")
                print(f"是否可用: {data.get('是否可用', 'N/A')}")
                
            except Exception as e:
                print(f"读取文件 {filename} 时出错: {e}")

def main():
    """主函数"""
    print("Excel转JSON文件转换器（优化版）")
    print("=" * 60)
    
    # 配置
    excel_file = "审核问答对（100-201）.xlsx"
    output_dir = "问答对100-201(优化后)"
    
    # 检查Excel文件是否存在
    if not os.path.exists(excel_file):
        print(f"错误: Excel文件不存在 - {excel_file}")
        print("请确保文件存在于当前目录中")
        return
    
    # 执行转换
    result = excel_to_json_files_optimized(excel_file, output_dir)
    
    if result > 0:
        print(f"\n✓ 转换成功! 共生成 {result} 个JSON文件")
        
        # 验证转换结果
        verify_conversion(output_dir)
        
        print(f"\n说明:")
        print(f"- 如果有'优化后的回答'，则使用优化后的回答作为'回答'字段")
        print(f"- 如果没有'优化后的回答'，则使用原'回答'字段")
        print(f"- 所有回答都统一保存在'回答'字段中")
        print(f"- 原始的'优化后的回答'字段也会保留用于记录")
        
    else:
        print("\n✗ 转换失败!")

if __name__ == "__main__":
    main()

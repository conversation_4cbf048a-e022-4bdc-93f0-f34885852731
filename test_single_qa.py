#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单个问答对测试脚本
用于验证多步骤合规评估系统的基本功能
"""

import json
import os
import sys

def test_single_qa():
    """测试单个问答对处理"""
    print("开始单个问答对测试...")
    
    # 检查是否存在测试文件
    test_file = "问答对100-201/001.json"
    if not os.path.exists(test_file):
        print(f"测试文件不存在: {test_file}")
        print("请确保问答对文件夹存在并包含测试数据")
        return False
    
    try:
        # 导入系统模块
        print("1. 导入系统模块...")
        from multi_stage_compliance_evaluator import MultiStageComplianceEvaluator
        print("✓ 模块导入成功")
        
        # 创建评估器
        print("2. 创建评估器...")
        evaluator = MultiStageComplianceEvaluator()
        print("✓ 评估器创建成功")
        
        # 加载测试文件
        print("3. 加载测试文件...")
        qa_data = evaluator.load_qa_file(test_file)
        if qa_data:
            print(f"✓ 文件加载成功")
            print(f"   问题: {qa_data.get('问题', 'N/A')}")
            print(f"   回答: {qa_data.get('回答', 'N/A')[:50]}...")
        else:
            print("✗ 文件加载失败")
            return False
        
        # 测试各个阶段（模拟，不实际调用API）
        print("4. 测试系统配置...")
        from multi_stage_config import QUESTION_TYPES, MULTI_STAGE_CONFIG
        print(f"✓ 问题类型配置: {len(QUESTION_TYPES)} 种类型")
        print(f"✓ 多阶段配置: {MULTI_STAGE_CONFIG['output_folder']}")
        
        # 检查输出目录
        print("5. 检查输出目录...")
        base_folder = MULTI_STAGE_CONFIG["output_folder"]
        if os.path.exists(base_folder):
            print(f"✓ 输出目录存在: {base_folder}")
        else:
            print(f"! 输出目录将被创建: {base_folder}")
        
        print("\n基本功能测试通过！")
        print("系统已准备好进行完整的多步骤评估。")
        
        # 询问是否进行实际API测试
        print("\n是否进行实际API调用测试？(y/n): ", end="")
        try:
            choice = input().lower().strip()
            if choice == 'y':
                return test_with_api(evaluator, qa_data)
            else:
                print("跳过API测试。")
                return True
        except:
            print("跳过API测试。")
            return True
            
    except ImportError as e:
        print(f"✗ 模块导入失败: {e}")
        print("请检查依赖是否正确安装")
        return False
    except Exception as e:
        print(f"✗ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_api(evaluator, qa_data):
    """使用API进行实际测试"""
    print("\n开始API调用测试...")
    
    try:
        # 测试第一阶段
        print("测试第一阶段 - 合理性检查...")
        stage1_result = evaluator.stage1_reasonableness_check(qa_data)
        
        if stage1_result.get("error"):
            print(f"✗ 第一阶段失败: {stage1_result['error']}")
            return False
        
        print(f"✓ 第一阶段完成")
        print(f"   判断: {stage1_result.get('reasonableness_judgment', 'N/A')}")
        print(f"   理由: {stage1_result.get('reasoning', 'N/A')}")
        
        # 如果第一阶段通过，测试第二阶段
        if stage1_result.get('reasonableness_judgment') == '合理':
            print("\n测试第二阶段 - 问题分类...")
            stage2_result = evaluator.stage2_question_classification(qa_data)
            
            if stage2_result.get("error"):
                print(f"✗ 第二阶段失败: {stage2_result['error']}")
                return False
            
            print(f"✓ 第二阶段完成")
            print(f"   分类: {stage2_result.get('question_type', 'N/A')}")
            print(f"   类型名: {stage2_result.get('type_name', 'N/A')}")
            
            # 测试第三阶段
            question_type = stage2_result.get('question_type')
            if question_type:
                print(f"\n测试第三阶段 - {question_type}类型评估...")
                stage3_result = evaluator.stage3_type_specific_evaluation(qa_data, question_type)
                
                if stage3_result.get("error"):
                    print(f"✗ 第三阶段失败: {stage3_result['error']}")
                    return False
                
                print(f"✓ 第三阶段完成")
                print(f"   合规判断: {stage3_result.get('compliance_judgment', 'N/A')}")
                
                # 测试第四阶段
                print("\n测试第四阶段 - 最终决策综合...")
                stage4_result = evaluator.stage4_final_decision(qa_data, stage1_result, stage2_result, stage3_result)
                
                if stage4_result.get("error"):
                    print(f"✗ 第四阶段失败: {stage4_result['error']}")
                    return False
                
                print(f"✓ 第四阶段完成")
                print(f"   最终判断: {stage4_result.get('final_compliance_judgment', 'N/A')}")
                
                print("\n✓ 所有阶段测试通过！")
                return True
        else:
            print("第一阶段判定问题不合理，流程正常终止。")
            return True
            
    except Exception as e:
        print(f"✗ API测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("多步骤合规评估系统 - 单个问答对测试")
    print("=" * 60)
    
    success = test_single_qa()
    
    print("\n" + "=" * 60)
    if success:
        print("测试完成 - 系统功能正常")
        print("\n下一步可以运行:")
        print("- python multi_stage_compliance_evaluator.py  # 完整批量评估")
        print("- python demo_multi_stage.py                  # 查看系统演示")
    else:
        print("测试失败 - 请检查系统配置")
    print("=" * 60)

if __name__ == "__main__":
    main()
